{% extends "base.html" %}

{% block title %}Add Song - Song Registry{% endblock %}

{% block content %}
<h2>Add New Song</h2>

<form id="add-song-form">
    <div class="form-group">
        <label for="title">Song Title:</label>
        <input type="text" id="title" name="title" required>
    </div>
    
    <div class="form-group">
        <label for="url">Song URL:</label>
        <input type="url" id="url" name="url" placeholder="https://example.com/song.mp3" required>
    </div>
    
    <div class="form-group">
        <label for="price">Price (in ETH):</label>
        <input type="number" id="price" name="price" step="0.001" min="0" placeholder="0.01" required>
    </div>
    
    <button type="submit">Register Song with MetaMask</button>
</form>

<div style="margin-top: 30px; padding: 15px; background-color: #fff3cd; border-radius: 4px; border: 1px solid #ffeaa7;">
    <h3>Important Notes:</h3>
    <ul>
        <li><strong>MetaMask Required:</strong> You need MetaMask installed to register songs</li>
        <li><strong>Sepolia Network:</strong> Make sure MetaMask is connected to Sepolia testnet</li>
        <li><strong>Gas Fees:</strong> You'll need Sepolia ETH to pay for transaction gas fees</li>
        <li><strong>Confirmation Time:</strong> It may take a few seconds for your transaction to be confirmed</li>
    </ul>
</div>
{% endblock %}
