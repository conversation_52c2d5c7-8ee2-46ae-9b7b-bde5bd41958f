{"id": "edbe53fb08726f40f7cf48ca64c94d48", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.20", "solcLongVersion": "0.8.20+commit.a1b79de6", "input": {"language": "Solidity", "sources": {"song-registry-contracts/contracts/songRegistry.sol": {"content": "// SPDX-License-Identifier: MIT\r\npragma solidity ^0.8.19;\r\n\r\ncontract SongRegistry {\r\n    struct Song {\r\n        string title;\r\n        address owner;\r\n        string url;\r\n        uint256 price;\r\n    }\r\n\r\n    Song[] public songs;\r\n\r\n    // Simple constructor\r\n    constructor() {}\r\n\r\n    function registerSong(string memory _title, string memory _url, uint256 _price) public {\r\n        songs.push(Song(_title, msg.sender, _url, _price));\r\n    }\r\n\r\n    function getNumberOfSongs() public view returns (uint256) {\r\n        return songs.length;\r\n    }\r\n}"}}, "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"song-registry-contracts/contracts/songRegistry.sol": {"ast": {"absolutePath": "song-registry-contracts/contracts/songRegistry.sol", "exportedSymbols": {"SongRegistry": [50]}, "id": 51, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".19"], "nodeType": "PragmaDirective", "src": "33:24:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "SongRegistry", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 50, "linearizedBaseContracts": [50], "name": "SongRegistry", "nameLocation": "70:12:0", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "SongRegistry.Song", "id": 10, "members": [{"constant": false, "id": 3, "mutability": "mutable", "name": "title", "nameLocation": "120:5:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "113:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2, "name": "string", "nodeType": "ElementaryTypeName", "src": "113:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 5, "mutability": "mutable", "name": "owner", "nameLocation": "144:5:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "136:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4, "name": "address", "nodeType": "ElementaryTypeName", "src": "136:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 7, "mutability": "mutable", "name": "url", "nameLocation": "167:3:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "160:10:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 6, "name": "string", "nodeType": "ElementaryTypeName", "src": "160:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 9, "mutability": "mutable", "name": "price", "nameLocation": "189:5:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "181:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "181:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "Song", "nameLocation": "97:4:0", "nodeType": "StructDefinition", "scope": 50, "src": "90:112:0", "visibility": "public"}, {"constant": false, "functionSelector": "304cff30", "id": 14, "mutability": "mutable", "name": "songs", "nameLocation": "224:5:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "210:19:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage", "typeString": "struct SongRegistry.Song[]"}, "typeName": {"baseType": {"id": 12, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 11, "name": "Song", "nameLocations": ["210:4:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 10, "src": "210:4:0"}, "referencedDeclaration": 10, "src": "210:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Song_$10_storage_ptr", "typeString": "struct SongRegistry.Song"}}, "id": 13, "nodeType": "ArrayTypeName", "src": "210:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage_ptr", "typeString": "struct SongRegistry.Song[]"}}, "visibility": "public"}, {"body": {"id": 17, "nodeType": "Block", "src": "279:2:0", "statements": []}, "id": 18, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 15, "nodeType": "ParameterList", "parameters": [], "src": "276:2:0"}, "returnParameters": {"id": 16, "nodeType": "ParameterList", "parameters": [], "src": "279:0:0"}, "scope": 50, "src": "265:16:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 39, "nodeType": "Block", "src": "376:69:0", "statements": [{"expression": {"arguments": [{"arguments": [{"id": 31, "name": "_title", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 20, "src": "403:6:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 32, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "411:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 33, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "415:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "411:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 34, "name": "_url", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 22, "src": "423:4:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 35, "name": "_price", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 24, "src": "429:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 30, "name": "Song", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10, "src": "398:4:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Song_$10_storage_ptr_$", "typeString": "type(struct SongRegistry.Song storage pointer)"}}, "id": 36, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "398:38:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Song_$10_memory_ptr", "typeString": "struct SongRegistry.Song memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Song_$10_memory_ptr", "typeString": "struct SongRegistry.Song memory"}], "expression": {"id": 27, "name": "songs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "387:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage", "typeString": "struct SongRegistry.Song storage ref[] storage ref"}}, "id": 29, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "393:4:0", "memberName": "push", "nodeType": "MemberAccess", "src": "387:10:0", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_Song_$10_storage_$dyn_storage_ptr_$_t_struct$_Song_$10_storage_$returns$__$attached_to$_t_array$_t_struct$_Song_$10_storage_$dyn_storage_ptr_$", "typeString": "function (struct SongRegistry.Song storage ref[] storage pointer,struct SongRegistry.Song storage ref)"}}, "id": 37, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "387:50:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 38, "nodeType": "ExpressionStatement", "src": "387:50:0"}]}, "functionSelector": "30753c7a", "id": 40, "implemented": true, "kind": "function", "modifiers": [], "name": "registerSong", "nameLocation": "298:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 25, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "mutability": "mutable", "name": "_title", "nameLocation": "325:6:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "311:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 19, "name": "string", "nodeType": "ElementaryTypeName", "src": "311:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 22, "mutability": "mutable", "name": "_url", "nameLocation": "347:4:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "333:18:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 21, "name": "string", "nodeType": "ElementaryTypeName", "src": "333:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 24, "mutability": "mutable", "name": "_price", "nameLocation": "361:6:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "353:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 23, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "353:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "310:58:0"}, "returnParameters": {"id": 26, "nodeType": "ParameterList", "parameters": [], "src": "376:0:0"}, "scope": 50, "src": "289:156:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 48, "nodeType": "Block", "src": "511:38:0", "statements": [{"expression": {"expression": {"id": 45, "name": "songs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "529:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage", "typeString": "struct SongRegistry.Song storage ref[] storage ref"}}, "id": 46, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "535:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "529:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 44, "id": 47, "nodeType": "Return", "src": "522:19:0"}]}, "functionSelector": "a8ec42d8", "id": 49, "implemented": true, "kind": "function", "modifiers": [], "name": "getNumberOfSongs", "nameLocation": "462:16:0", "nodeType": "FunctionDefinition", "parameters": {"id": 41, "nodeType": "ParameterList", "parameters": [], "src": "478:2:0"}, "returnParameters": {"id": 44, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 49, "src": "502:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "502:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "501:9:0"}, "scope": 50, "src": "453:96:0", "stateMutability": "view", "virtual": false, "visibility": "public"}], "scope": 51, "src": "61:491:0", "usedErrors": [], "usedEvents": []}], "src": "33:519:0"}, "id": 0}}, "contracts": {"song-registry-contracts/contracts/songRegistry.sol": {"SongRegistry": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "getNumberOfSongs", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_title", "type": "string"}, {"internalType": "string", "name": "_url", "type": "string"}, {"internalType": "uint256", "name": "_price", "type": "uint256"}], "name": "registerSong", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "songs", "outputs": [{"internalType": "string", "name": "title", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "string", "name": "url", "type": "string"}, {"internalType": "uint256", "name": "price", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {"@_18": {"entryPoint": null, "id": 18, "parameterSlots": 0, "returnSlots": 0}}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x9AE DUP1 PUSH2 0x20 PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x41 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x304CFF30 EQ PUSH2 0x46 JUMPI DUP1 PUSH4 0x30753C7A EQ PUSH2 0x79 JUMPI DUP1 PUSH4 0xA8EC42D8 EQ PUSH2 0x95 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x60 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x5B SWAP2 SWAP1 PUSH2 0x35F JUMP JUMPDEST PUSH2 0xB3 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x70 SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x46C JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x93 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x8E SWAP2 SWAP1 PUSH2 0x5F4 JUMP JUMPDEST PUSH2 0x223 JUMP JUMPDEST STOP JUMPDEST PUSH2 0x9D PUSH2 0x309 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xAA SWAP2 SWAP1 PUSH2 0x67F JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0xC3 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x4 MUL ADD PUSH1 0x0 SWAP2 POP SWAP1 POP DUP1 PUSH1 0x0 ADD DUP1 SLOAD PUSH2 0xE6 SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x112 SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x15F JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x134 JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x15F JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x142 JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP SWAP1 DUP1 PUSH1 0x1 ADD PUSH1 0x0 SWAP1 SLOAD SWAP1 PUSH2 0x100 EXP SWAP1 DIV PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND SWAP1 DUP1 PUSH1 0x2 ADD DUP1 SLOAD PUSH2 0x19A SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x1C6 SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x213 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x1E8 JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x213 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x1F6 JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP SWAP1 DUP1 PUSH1 0x3 ADD SLOAD SWAP1 POP DUP5 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 PUSH1 0x80 ADD PUSH1 0x40 MSTORE DUP1 DUP6 DUP2 MSTORE PUSH1 0x20 ADD CALLER PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD DUP5 DUP2 MSTORE PUSH1 0x20 ADD DUP4 DUP2 MSTORE POP SWAP1 DUP1 PUSH1 0x1 DUP2 SLOAD ADD DUP1 DUP3 SSTORE DUP1 SWAP2 POP POP PUSH1 0x1 SWAP1 SUB SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x4 MUL ADD PUSH1 0x0 SWAP1 SWAP2 SWAP1 SWAP2 SWAP1 SWAP2 POP PUSH1 0x0 DUP3 ADD MLOAD DUP2 PUSH1 0x0 ADD SWAP1 DUP2 PUSH2 0x29A SWAP2 SWAP1 PUSH2 0x8A6 JUMP JUMPDEST POP PUSH1 0x20 DUP3 ADD MLOAD DUP2 PUSH1 0x1 ADD PUSH1 0x0 PUSH2 0x100 EXP DUP2 SLOAD DUP2 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF MUL NOT AND SWAP1 DUP4 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND MUL OR SWAP1 SSTORE POP PUSH1 0x40 DUP3 ADD MLOAD DUP2 PUSH1 0x2 ADD SWAP1 DUP2 PUSH2 0x2F7 SWAP2 SWAP1 PUSH2 0x8A6 JUMP JUMPDEST POP PUSH1 0x60 DUP3 ADD MLOAD DUP2 PUSH1 0x3 ADD SSTORE POP POP POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 DUP1 SLOAD SWAP1 POP SWAP1 POP SWAP1 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD SWAP1 POP SWAP1 JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x33C DUP2 PUSH2 0x329 JUMP JUMPDEST DUP2 EQ PUSH2 0x347 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x359 DUP2 PUSH2 0x333 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x375 JUMPI PUSH2 0x374 PUSH2 0x31F JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x383 DUP5 DUP3 DUP6 ADD PUSH2 0x34A JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x3C6 JUMPI DUP1 DUP3 ADD MLOAD DUP2 DUP5 ADD MSTORE PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x3AB JUMP JUMPDEST PUSH1 0x0 DUP5 DUP5 ADD MSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x3EE DUP3 PUSH2 0x38C JUMP JUMPDEST PUSH2 0x3F8 DUP2 DUP6 PUSH2 0x397 JUMP JUMPDEST SWAP4 POP PUSH2 0x408 DUP2 DUP6 PUSH1 0x20 DUP7 ADD PUSH2 0x3A8 JUMP JUMPDEST PUSH2 0x411 DUP2 PUSH2 0x3D2 JUMP JUMPDEST DUP5 ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x447 DUP3 PUSH2 0x41C JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x457 DUP2 PUSH2 0x43C JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH2 0x466 DUP2 PUSH2 0x329 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x80 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x486 DUP2 DUP8 PUSH2 0x3E3 JUMP JUMPDEST SWAP1 POP PUSH2 0x495 PUSH1 0x20 DUP4 ADD DUP7 PUSH2 0x44E JUMP JUMPDEST DUP2 DUP2 SUB PUSH1 0x40 DUP4 ADD MSTORE PUSH2 0x4A7 DUP2 DUP6 PUSH2 0x3E3 JUMP JUMPDEST SWAP1 POP PUSH2 0x4B6 PUSH1 0x60 DUP4 ADD DUP5 PUSH2 0x45D JUMP JUMPDEST SWAP6 SWAP5 POP POP POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH2 0x501 DUP3 PUSH2 0x3D2 JUMP JUMPDEST DUP2 ADD DUP2 DUP2 LT PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT OR ISZERO PUSH2 0x520 JUMPI PUSH2 0x51F PUSH2 0x4C9 JUMP JUMPDEST JUMPDEST DUP1 PUSH1 0x40 MSTORE POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x533 PUSH2 0x315 JUMP JUMPDEST SWAP1 POP PUSH2 0x53F DUP3 DUP3 PUSH2 0x4F8 JUMP JUMPDEST SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT ISZERO PUSH2 0x55F JUMPI PUSH2 0x55E PUSH2 0x4C9 JUMP JUMPDEST JUMPDEST PUSH2 0x568 DUP3 PUSH2 0x3D2 JUMP JUMPDEST SWAP1 POP PUSH1 0x20 DUP2 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST DUP3 DUP2 DUP4 CALLDATACOPY PUSH1 0x0 DUP4 DUP4 ADD MSTORE POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x597 PUSH2 0x592 DUP5 PUSH2 0x544 JUMP JUMPDEST PUSH2 0x529 JUMP JUMPDEST SWAP1 POP DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP5 DUP5 DUP5 ADD GT ISZERO PUSH2 0x5B3 JUMPI PUSH2 0x5B2 PUSH2 0x4C4 JUMP JUMPDEST JUMPDEST PUSH2 0x5BE DUP5 DUP3 DUP6 PUSH2 0x575 JUMP JUMPDEST POP SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP3 PUSH1 0x1F DUP4 ADD SLT PUSH2 0x5DB JUMPI PUSH2 0x5DA PUSH2 0x4BF JUMP JUMPDEST JUMPDEST DUP2 CALLDATALOAD PUSH2 0x5EB DUP5 DUP3 PUSH1 0x20 DUP7 ADD PUSH2 0x584 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x0 PUSH1 0x60 DUP5 DUP7 SUB SLT ISZERO PUSH2 0x60D JUMPI PUSH2 0x60C PUSH2 0x31F JUMP JUMPDEST JUMPDEST PUSH1 0x0 DUP5 ADD CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x62B JUMPI PUSH2 0x62A PUSH2 0x324 JUMP JUMPDEST JUMPDEST PUSH2 0x637 DUP7 DUP3 DUP8 ADD PUSH2 0x5C6 JUMP JUMPDEST SWAP4 POP POP PUSH1 0x20 DUP5 ADD CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x658 JUMPI PUSH2 0x657 PUSH2 0x324 JUMP JUMPDEST JUMPDEST PUSH2 0x664 DUP7 DUP3 DUP8 ADD PUSH2 0x5C6 JUMP JUMPDEST SWAP3 POP POP PUSH1 0x40 PUSH2 0x675 DUP7 DUP3 DUP8 ADD PUSH2 0x34A JUMP JUMPDEST SWAP2 POP POP SWAP3 POP SWAP3 POP SWAP3 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x694 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x45D JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH1 0x2 DUP3 DIV SWAP1 POP PUSH1 0x1 DUP3 AND DUP1 PUSH2 0x6E1 JUMPI PUSH1 0x7F DUP3 AND SWAP2 POP JUMPDEST PUSH1 0x20 DUP3 LT DUP2 SUB PUSH2 0x6F4 JUMPI PUSH2 0x6F3 PUSH2 0x69A JUMP JUMPDEST JUMPDEST POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP DUP2 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 PUSH1 0x1F DUP4 ADD DIV SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 SHL SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x8 DUP4 MUL PUSH2 0x75C PUSH32 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF DUP3 PUSH2 0x71F JUMP JUMPDEST PUSH2 0x766 DUP7 DUP4 PUSH2 0x71F JUMP JUMPDEST SWAP6 POP DUP1 NOT DUP5 AND SWAP4 POP DUP1 DUP7 AND DUP5 OR SWAP3 POP POP POP SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7A3 PUSH2 0x79E PUSH2 0x799 DUP5 PUSH2 0x329 JUMP JUMPDEST PUSH2 0x77E JUMP JUMPDEST PUSH2 0x329 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x7BD DUP4 PUSH2 0x788 JUMP JUMPDEST PUSH2 0x7D1 PUSH2 0x7C9 DUP3 PUSH2 0x7AA JUMP JUMPDEST DUP5 DUP5 SLOAD PUSH2 0x72C JUMP JUMPDEST DUP3 SSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 SWAP1 JUMP JUMPDEST PUSH2 0x7E6 PUSH2 0x7D9 JUMP JUMPDEST PUSH2 0x7F1 DUP2 DUP5 DUP5 PUSH2 0x7B4 JUMP JUMPDEST POP POP POP JUMP JUMPDEST JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x815 JUMPI PUSH2 0x80A PUSH1 0x0 DUP3 PUSH2 0x7DE JUMP JUMPDEST PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x7F7 JUMP JUMPDEST POP POP JUMP JUMPDEST PUSH1 0x1F DUP3 GT ISZERO PUSH2 0x85A JUMPI PUSH2 0x82B DUP2 PUSH2 0x6FA JUMP JUMPDEST PUSH2 0x834 DUP5 PUSH2 0x70F JUMP JUMPDEST DUP2 ADD PUSH1 0x20 DUP6 LT ISZERO PUSH2 0x843 JUMPI DUP2 SWAP1 POP JUMPDEST PUSH2 0x857 PUSH2 0x84F DUP6 PUSH2 0x70F JUMP JUMPDEST DUP4 ADD DUP3 PUSH2 0x7F6 JUMP JUMPDEST POP POP JUMPDEST POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 SHR SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x87D PUSH1 0x0 NOT DUP5 PUSH1 0x8 MUL PUSH2 0x85F JUMP JUMPDEST NOT DUP1 DUP4 AND SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x896 DUP4 DUP4 PUSH2 0x86C JUMP JUMPDEST SWAP2 POP DUP3 PUSH1 0x2 MUL DUP3 OR SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH2 0x8AF DUP3 PUSH2 0x38C JUMP JUMPDEST PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x8C8 JUMPI PUSH2 0x8C7 PUSH2 0x4C9 JUMP JUMPDEST JUMPDEST PUSH2 0x8D2 DUP3 SLOAD PUSH2 0x6C9 JUMP JUMPDEST PUSH2 0x8DD DUP3 DUP3 DUP6 PUSH2 0x819 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 SWAP1 POP PUSH1 0x1F DUP4 GT PUSH1 0x1 DUP2 EQ PUSH2 0x910 JUMPI PUSH1 0x0 DUP5 ISZERO PUSH2 0x8FE JUMPI DUP3 DUP8 ADD MLOAD SWAP1 POP JUMPDEST PUSH2 0x908 DUP6 DUP3 PUSH2 0x88A JUMP JUMPDEST DUP7 SSTORE POP PUSH2 0x970 JUMP JUMPDEST PUSH1 0x1F NOT DUP5 AND PUSH2 0x91E DUP7 PUSH2 0x6FA JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP3 DUP2 LT ISZERO PUSH2 0x946 JUMPI DUP5 DUP10 ADD MLOAD DUP3 SSTORE PUSH1 0x1 DUP3 ADD SWAP2 POP PUSH1 0x20 DUP6 ADD SWAP5 POP PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x921 JUMP JUMPDEST DUP7 DUP4 LT ISZERO PUSH2 0x963 JUMPI DUP5 DUP10 ADD MLOAD PUSH2 0x95F PUSH1 0x1F DUP10 AND DUP3 PUSH2 0x86C JUMP JUMPDEST DUP4 SSTORE POP JUMPDEST PUSH1 0x1 PUSH1 0x2 DUP9 MUL ADD DUP9 SSTORE POP POP POP JUMPDEST POP POP POP POP POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 LOG1 REVERT 0xC5 DUP8 XOR PC AND 0xBE KECCAK256 0xE4 SWAP2 PUSH28 0x9FF72F3273ABBB234E278DB13F9123BD4C5F7BD664736F6C63430008 EQ STOP CALLER ", "sourceMap": "61:491:0:-:0;;;265:16;;;;;;;;;;61:491;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@getNumberOfSongs_49": {"entryPoint": 777, "id": 49, "parameterSlots": 0, "returnSlots": 1}, "@registerSong_40": {"entryPoint": 547, "id": 40, "parameterSlots": 3, "returnSlots": 0}, "@songs_14": {"entryPoint": 179, "id": 14, "parameterSlots": 0, "returnSlots": 0}, "abi_decode_available_length_t_string_memory_ptr": {"entryPoint": 1412, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_decode_t_string_memory_ptr": {"entryPoint": 1478, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_t_uint256": {"entryPoint": 842, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_string_memory_ptrt_string_memory_ptrt_uint256": {"entryPoint": 1524, "id": null, "parameterSlots": 2, "returnSlots": 3}, "abi_decode_tuple_t_uint256": {"entryPoint": 863, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_address_to_t_address_fromStack": {"entryPoint": 1102, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack": {"entryPoint": 995, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_uint256_to_t_uint256_fromStack": {"entryPoint": 1117, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_tuple_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__fromStack_reversed": {"entryPoint": 1132, "id": null, "parameterSlots": 5, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": 1663, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_memory": {"entryPoint": 1321, "id": null, "parameterSlots": 1, "returnSlots": 1}, "allocate_unbounded": {"entryPoint": 789, "id": null, "parameterSlots": 0, "returnSlots": 1}, "array_allocation_size_t_string_memory_ptr": {"entryPoint": 1348, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_dataslot_t_string_storage": {"entryPoint": 1786, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_length_t_string_memory_ptr": {"entryPoint": 908, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_storeLengthForEncoding_t_string_memory_ptr_fromStack": {"entryPoint": 919, "id": null, "parameterSlots": 2, "returnSlots": 1}, "clean_up_bytearray_end_slots_t_string_storage": {"entryPoint": 2073, "id": null, "parameterSlots": 3, "returnSlots": 0}, "cleanup_t_address": {"entryPoint": 1084, "id": null, "parameterSlots": 1, "returnSlots": 1}, "cleanup_t_uint160": {"entryPoint": 1052, "id": null, "parameterSlots": 1, "returnSlots": 1}, "cleanup_t_uint256": {"entryPoint": 809, "id": null, "parameterSlots": 1, "returnSlots": 1}, "clear_storage_range_t_bytes1": {"entryPoint": 2038, "id": null, "parameterSlots": 2, "returnSlots": 0}, "convert_t_uint256_to_t_uint256": {"entryPoint": 1928, "id": null, "parameterSlots": 1, "returnSlots": 1}, "copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage": {"entryPoint": 2214, "id": null, "parameterSlots": 2, "returnSlots": 0}, "copy_calldata_to_memory_with_cleanup": {"entryPoint": 1397, "id": null, "parameterSlots": 3, "returnSlots": 0}, "copy_memory_to_memory_with_cleanup": {"entryPoint": 936, "id": null, "parameterSlots": 3, "returnSlots": 0}, "divide_by_32_ceil": {"entryPoint": 1807, "id": null, "parameterSlots": 1, "returnSlots": 1}, "extract_byte_array_length": {"entryPoint": 1737, "id": null, "parameterSlots": 1, "returnSlots": 1}, "extract_used_part_and_set_length_of_short_byte_array": {"entryPoint": 2186, "id": null, "parameterSlots": 2, "returnSlots": 1}, "finalize_allocation": {"entryPoint": 1272, "id": null, "parameterSlots": 2, "returnSlots": 0}, "identity": {"entryPoint": 1918, "id": null, "parameterSlots": 1, "returnSlots": 1}, "mask_bytes_dynamic": {"entryPoint": 2156, "id": null, "parameterSlots": 2, "returnSlots": 1}, "panic_error_0x22": {"entryPoint": 1690, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x41": {"entryPoint": 1225, "id": null, "parameterSlots": 0, "returnSlots": 0}, "prepare_store_t_uint256": {"entryPoint": 1962, "id": null, "parameterSlots": 1, "returnSlots": 1}, "revert_error_1b9f4a0a5773e33b91aa01db23bf8c55fce1411167c872835e7fa00a4f17d46d": {"entryPoint": 1215, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_987264b3b1d58a9c7f8255e93e81c77d86d6299019c33110a076957a3e06e2ae": {"entryPoint": 1220, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db": {"entryPoint": 804, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b": {"entryPoint": 799, "id": null, "parameterSlots": 0, "returnSlots": 0}, "round_up_to_mul_of_32": {"entryPoint": 978, "id": null, "parameterSlots": 1, "returnSlots": 1}, "shift_left_dynamic": {"entryPoint": 1823, "id": null, "parameterSlots": 2, "returnSlots": 1}, "shift_right_unsigned_dynamic": {"entryPoint": 2143, "id": null, "parameterSlots": 2, "returnSlots": 1}, "storage_set_to_zero_t_uint256": {"entryPoint": 2014, "id": null, "parameterSlots": 2, "returnSlots": 0}, "update_byte_slice_dynamic32": {"entryPoint": 1836, "id": null, "parameterSlots": 3, "returnSlots": 1}, "update_storage_value_t_uint256_to_t_uint256": {"entryPoint": 1972, "id": null, "parameterSlots": 3, "returnSlots": 0}, "validator_revert_t_uint256": {"entryPoint": 819, "id": null, "parameterSlots": 1, "returnSlots": 0}, "zero_value_for_split_t_uint256": {"entryPoint": 2009, "id": null, "parameterSlots": 0, "returnSlots": 1}}, "generatedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:11445:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "47:35:1", "statements": [{"nodeType": "YulAssignment", "src": "57:19:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "73:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "67:5:1"}, "nodeType": "YulFunctionCall", "src": "67:9:1"}, "variableNames": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "57:6:1"}]}]}, "name": "allocate_unbounded", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "memPtr", "nodeType": "YulTypedName", "src": "40:6:1", "type": ""}], "src": "7:75:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "177:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "194:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "197:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "187:6:1"}, "nodeType": "YulFunctionCall", "src": "187:12:1"}, "nodeType": "YulExpressionStatement", "src": "187:12:1"}]}, "name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nodeType": "YulFunctionDefinition", "src": "88:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "300:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "317:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "320:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "310:6:1"}, "nodeType": "YulFunctionCall", "src": "310:12:1"}, "nodeType": "YulExpressionStatement", "src": "310:12:1"}]}, "name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nodeType": "YulFunctionDefinition", "src": "211:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "379:32:1", "statements": [{"nodeType": "YulAssignment", "src": "389:16:1", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "400:5:1"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "389:7:1"}]}]}, "name": "cleanup_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "361:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "371:7:1", "type": ""}], "src": "334:77:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "460:79:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "517:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "526:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "529:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "519:6:1"}, "nodeType": "YulFunctionCall", "src": "519:12:1"}, "nodeType": "YulExpressionStatement", "src": "519:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "483:5:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "508:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "490:17:1"}, "nodeType": "YulFunctionCall", "src": "490:24:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "480:2:1"}, "nodeType": "YulFunctionCall", "src": "480:35:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "473:6:1"}, "nodeType": "YulFunctionCall", "src": "473:43:1"}, "nodeType": "YulIf", "src": "470:63:1"}]}, "name": "validator_revert_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "453:5:1", "type": ""}], "src": "417:122:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "597:87:1", "statements": [{"nodeType": "YulAssignment", "src": "607:29:1", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "629:6:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "616:12:1"}, "nodeType": "YulFunctionCall", "src": "616:20:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "607:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "672:5:1"}], "functionName": {"name": "validator_revert_t_uint256", "nodeType": "YulIdentifier", "src": "645:26:1"}, "nodeType": "YulFunctionCall", "src": "645:33:1"}, "nodeType": "YulExpressionStatement", "src": "645:33:1"}]}, "name": "abi_decode_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "575:6:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "583:3:1", "type": ""}], "returnVariables": [{"name": "value", "nodeType": "YulTypedName", "src": "591:5:1", "type": ""}], "src": "545:139:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "756:263:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "802:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nodeType": "YulIdentifier", "src": "804:77:1"}, "nodeType": "YulFunctionCall", "src": "804:79:1"}, "nodeType": "YulExpressionStatement", "src": "804:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "777:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "786:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "773:3:1"}, "nodeType": "YulFunctionCall", "src": "773:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "798:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "769:3:1"}, "nodeType": "YulFunctionCall", "src": "769:32:1"}, "nodeType": "YulIf", "src": "766:119:1"}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "895:117:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "910:15:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "924:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "914:6:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "939:63:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "974:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "985:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "970:3:1"}, "nodeType": "YulFunctionCall", "src": "970:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "994:7:1"}], "functionName": {"name": "abi_decode_t_uint256", "nodeType": "YulIdentifier", "src": "949:20:1"}, "nodeType": "YulFunctionCall", "src": "949:53:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "939:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "726:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "737:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "749:6:1", "type": ""}], "src": "690:329:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1084:40:1", "statements": [{"nodeType": "YulAssignment", "src": "1095:22:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1111:5:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "1105:5:1"}, "nodeType": "YulFunctionCall", "src": "1105:12:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "1095:6:1"}]}]}, "name": "array_length_t_string_memory_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1067:5:1", "type": ""}], "returnVariables": [{"name": "length", "nodeType": "YulTypedName", "src": "1077:6:1", "type": ""}], "src": "1025:99:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1226:73:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1243:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1248:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1236:6:1"}, "nodeType": "YulFunctionCall", "src": "1236:19:1"}, "nodeType": "YulExpressionStatement", "src": "1236:19:1"}, {"nodeType": "YulAssignment", "src": "1264:29:1", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1283:3:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1288:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1279:3:1"}, "nodeType": "YulFunctionCall", "src": "1279:14:1"}, "variableNames": [{"name": "updated_pos", "nodeType": "YulIdentifier", "src": "1264:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "1198:3:1", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "1203:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nodeType": "YulTypedName", "src": "1214:11:1", "type": ""}], "src": "1130:169:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1367:184:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1377:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1386:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "1381:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1446:63:1", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "1471:3:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "1476:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1467:3:1"}, "nodeType": "YulFunctionCall", "src": "1467:11:1"}, {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "1490:3:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "1495:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1486:3:1"}, "nodeType": "YulFunctionCall", "src": "1486:11:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "1480:5:1"}, "nodeType": "YulFunctionCall", "src": "1480:18:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1460:6:1"}, "nodeType": "YulFunctionCall", "src": "1460:39:1"}, "nodeType": "YulExpressionStatement", "src": "1460:39:1"}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "1407:1:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1410:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "1404:2:1"}, "nodeType": "YulFunctionCall", "src": "1404:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1418:19:1", "statements": [{"nodeType": "YulAssignment", "src": "1420:15:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "1429:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1432:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1425:3:1"}, "nodeType": "YulFunctionCall", "src": "1425:10:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "1420:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1400:3:1", "statements": []}, "src": "1396:113:1"}, {"expression": {"arguments": [{"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "1529:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1534:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1525:3:1"}, "nodeType": "YulFunctionCall", "src": "1525:16:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1543:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1518:6:1"}, "nodeType": "YulFunctionCall", "src": "1518:27:1"}, "nodeType": "YulExpressionStatement", "src": "1518:27:1"}]}, "name": "copy_memory_to_memory_with_cleanup", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nodeType": "YulTypedName", "src": "1349:3:1", "type": ""}, {"name": "dst", "nodeType": "YulTypedName", "src": "1354:3:1", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "1359:6:1", "type": ""}], "src": "1305:246:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1605:54:1", "statements": [{"nodeType": "YulAssignment", "src": "1615:38:1", "value": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1633:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1640:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1629:3:1"}, "nodeType": "YulFunctionCall", "src": "1629:14:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1649:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "1645:3:1"}, "nodeType": "YulFunctionCall", "src": "1645:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "1625:3:1"}, "nodeType": "YulFunctionCall", "src": "1625:28:1"}, "variableNames": [{"name": "result", "nodeType": "YulIdentifier", "src": "1615:6:1"}]}]}, "name": "round_up_to_mul_of_32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1588:5:1", "type": ""}], "returnVariables": [{"name": "result", "nodeType": "YulTypedName", "src": "1598:6:1", "type": ""}], "src": "1557:102:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1757:285:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1767:53:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1814:5:1"}], "functionName": {"name": "array_length_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "1781:32:1"}, "nodeType": "YulFunctionCall", "src": "1781:39:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "1771:6:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "1829:78:1", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1895:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1900:6:1"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "1836:58:1"}, "nodeType": "YulFunctionCall", "src": "1836:71:1"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1829:3:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1955:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1962:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1951:3:1"}, "nodeType": "YulFunctionCall", "src": "1951:16:1"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "1969:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "1974:6:1"}], "functionName": {"name": "copy_memory_to_memory_with_cleanup", "nodeType": "YulIdentifier", "src": "1916:34:1"}, "nodeType": "YulFunctionCall", "src": "1916:65:1"}, "nodeType": "YulExpressionStatement", "src": "1916:65:1"}, {"nodeType": "YulAssignment", "src": "1990:46:1", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2001:3:1"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "2028:6:1"}], "functionName": {"name": "round_up_to_mul_of_32", "nodeType": "YulIdentifier", "src": "2006:21:1"}, "nodeType": "YulFunctionCall", "src": "2006:29:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1997:3:1"}, "nodeType": "YulFunctionCall", "src": "1997:39:1"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "1990:3:1"}]}]}, "name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1738:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "1745:3:1", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "1753:3:1", "type": ""}], "src": "1665:377:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2093:81:1", "statements": [{"nodeType": "YulAssignment", "src": "2103:65:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2118:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2125:42:1", "type": "", "value": "0xffffffffffffffffffffffffffffffffffffffff"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "2114:3:1"}, "nodeType": "YulFunctionCall", "src": "2114:54:1"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "2103:7:1"}]}]}, "name": "cleanup_t_uint160", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2075:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "2085:7:1", "type": ""}], "src": "2048:126:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2225:51:1", "statements": [{"nodeType": "YulAssignment", "src": "2235:35:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2264:5:1"}], "functionName": {"name": "cleanup_t_uint160", "nodeType": "YulIdentifier", "src": "2246:17:1"}, "nodeType": "YulFunctionCall", "src": "2246:24:1"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "2235:7:1"}]}]}, "name": "cleanup_t_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2207:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "2217:7:1", "type": ""}], "src": "2180:96:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2347:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2364:3:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2387:5:1"}], "functionName": {"name": "cleanup_t_address", "nodeType": "YulIdentifier", "src": "2369:17:1"}, "nodeType": "YulFunctionCall", "src": "2369:24:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2357:6:1"}, "nodeType": "YulFunctionCall", "src": "2357:37:1"}, "nodeType": "YulExpressionStatement", "src": "2357:37:1"}]}, "name": "abi_encode_t_address_to_t_address_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2335:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "2342:3:1", "type": ""}], "src": "2282:118:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2471:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2488:3:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2511:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "2493:17:1"}, "nodeType": "YulFunctionCall", "src": "2493:24:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2481:6:1"}, "nodeType": "YulFunctionCall", "src": "2481:37:1"}, "nodeType": "YulExpressionStatement", "src": "2481:37:1"}]}, "name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2459:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "2466:3:1", "type": ""}], "src": "2406:118:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2752:513:1", "statements": [{"nodeType": "YulAssignment", "src": "2762:27:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2774:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2785:3:1", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2770:3:1"}, "nodeType": "YulFunctionCall", "src": "2770:19:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2762:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2810:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2821:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2806:3:1"}, "nodeType": "YulFunctionCall", "src": "2806:17:1"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2829:4:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "2835:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "2825:3:1"}, "nodeType": "YulFunctionCall", "src": "2825:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2799:6:1"}, "nodeType": "YulFunctionCall", "src": "2799:47:1"}, "nodeType": "YulExpressionStatement", "src": "2799:47:1"}, {"nodeType": "YulAssignment", "src": "2855:86:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "2927:6:1"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "2936:4:1"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "2863:63:1"}, "nodeType": "YulFunctionCall", "src": "2863:78:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2855:4:1"}]}, {"expression": {"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "2995:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3008:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3019:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3004:3:1"}, "nodeType": "YulFunctionCall", "src": "3004:18:1"}], "functionName": {"name": "abi_encode_t_address_to_t_address_fromStack", "nodeType": "YulIdentifier", "src": "2951:43:1"}, "nodeType": "YulFunctionCall", "src": "2951:72:1"}, "nodeType": "YulExpressionStatement", "src": "2951:72:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3044:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3055:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3040:3:1"}, "nodeType": "YulFunctionCall", "src": "3040:18:1"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "3064:4:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "3070:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "3060:3:1"}, "nodeType": "YulFunctionCall", "src": "3060:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3033:6:1"}, "nodeType": "YulFunctionCall", "src": "3033:48:1"}, "nodeType": "YulExpressionStatement", "src": "3033:48:1"}, {"nodeType": "YulAssignment", "src": "3090:86:1", "value": {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "3162:6:1"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "3171:4:1"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "3098:63:1"}, "nodeType": "YulFunctionCall", "src": "3098:78:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "3090:4:1"}]}, {"expression": {"arguments": [{"name": "value3", "nodeType": "YulIdentifier", "src": "3230:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3243:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3254:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3239:3:1"}, "nodeType": "YulFunctionCall", "src": "3239:18:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "3186:43:1"}, "nodeType": "YulFunctionCall", "src": "3186:72:1"}, "nodeType": "YulExpressionStatement", "src": "3186:72:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2700:9:1", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "2712:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "2720:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "2728:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "2736:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2747:4:1", "type": ""}], "src": "2530:735:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3360:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3377:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3380:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "3370:6:1"}, "nodeType": "YulFunctionCall", "src": "3370:12:1"}, "nodeType": "YulExpressionStatement", "src": "3370:12:1"}]}, "name": "revert_error_1b9f4a0a5773e33b91aa01db23bf8c55fce1411167c872835e7fa00a4f17d46d", "nodeType": "YulFunctionDefinition", "src": "3271:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3483:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3500:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3503:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "3493:6:1"}, "nodeType": "YulFunctionCall", "src": "3493:12:1"}, "nodeType": "YulExpressionStatement", "src": "3493:12:1"}]}, "name": "revert_error_987264b3b1d58a9c7f8255e93e81c77d86d6299019c33110a076957a3e06e2ae", "nodeType": "YulFunctionDefinition", "src": "3394:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3545:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3562:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3565:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3555:6:1"}, "nodeType": "YulFunctionCall", "src": "3555:88:1"}, "nodeType": "YulExpressionStatement", "src": "3555:88:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3659:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3662:4:1", "type": "", "value": "0x41"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3652:6:1"}, "nodeType": "YulFunctionCall", "src": "3652:15:1"}, "nodeType": "YulExpressionStatement", "src": "3652:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3683:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3686:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "3676:6:1"}, "nodeType": "YulFunctionCall", "src": "3676:15:1"}, "nodeType": "YulExpressionStatement", "src": "3676:15:1"}]}, "name": "panic_error_0x41", "nodeType": "YulFunctionDefinition", "src": "3517:180:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3746:238:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "3756:58:1", "value": {"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "3778:6:1"}, {"arguments": [{"name": "size", "nodeType": "YulIdentifier", "src": "3808:4:1"}], "functionName": {"name": "round_up_to_mul_of_32", "nodeType": "YulIdentifier", "src": "3786:21:1"}, "nodeType": "YulFunctionCall", "src": "3786:27:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3774:3:1"}, "nodeType": "YulFunctionCall", "src": "3774:40:1"}, "variables": [{"name": "newFreePtr", "nodeType": "YulTypedName", "src": "3760:10:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3925:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "3927:16:1"}, "nodeType": "YulFunctionCall", "src": "3927:18:1"}, "nodeType": "YulExpressionStatement", "src": "3927:18:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "3868:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3880:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "3865:2:1"}, "nodeType": "YulFunctionCall", "src": "3865:34:1"}, {"arguments": [{"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "3904:10:1"}, {"name": "memPtr", "nodeType": "YulIdentifier", "src": "3916:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "3901:2:1"}, "nodeType": "YulFunctionCall", "src": "3901:22:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "3862:2:1"}, "nodeType": "YulFunctionCall", "src": "3862:62:1"}, "nodeType": "YulIf", "src": "3859:88:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3963:2:1", "type": "", "value": "64"}, {"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "3967:10:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3956:6:1"}, "nodeType": "YulFunctionCall", "src": "3956:22:1"}, "nodeType": "YulExpressionStatement", "src": "3956:22:1"}]}, "name": "finalize_allocation", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "memPtr", "nodeType": "YulTypedName", "src": "3732:6:1", "type": ""}, {"name": "size", "nodeType": "YulTypedName", "src": "3740:4:1", "type": ""}], "src": "3703:281:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4031:88:1", "statements": [{"nodeType": "YulAssignment", "src": "4041:30:1", "value": {"arguments": [], "functionName": {"name": "allocate_unbounded", "nodeType": "YulIdentifier", "src": "4051:18:1"}, "nodeType": "YulFunctionCall", "src": "4051:20:1"}, "variableNames": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "4041:6:1"}]}, {"expression": {"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "4100:6:1"}, {"name": "size", "nodeType": "YulIdentifier", "src": "4108:4:1"}], "functionName": {"name": "finalize_allocation", "nodeType": "YulIdentifier", "src": "4080:19:1"}, "nodeType": "YulFunctionCall", "src": "4080:33:1"}, "nodeType": "YulExpressionStatement", "src": "4080:33:1"}]}, "name": "allocate_memory", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "size", "nodeType": "YulTypedName", "src": "4015:4:1", "type": ""}], "returnVariables": [{"name": "memPtr", "nodeType": "YulTypedName", "src": "4024:6:1", "type": ""}], "src": "3990:129:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4192:241:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4297:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "4299:16:1"}, "nodeType": "YulFunctionCall", "src": "4299:18:1"}, "nodeType": "YulExpressionStatement", "src": "4299:18:1"}]}, "condition": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "4269:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4277:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4266:2:1"}, "nodeType": "YulFunctionCall", "src": "4266:30:1"}, "nodeType": "YulIf", "src": "4263:56:1"}, {"nodeType": "YulAssignment", "src": "4329:37:1", "value": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "4359:6:1"}], "functionName": {"name": "round_up_to_mul_of_32", "nodeType": "YulIdentifier", "src": "4337:21:1"}, "nodeType": "YulFunctionCall", "src": "4337:29:1"}, "variableNames": [{"name": "size", "nodeType": "YulIdentifier", "src": "4329:4:1"}]}, {"nodeType": "YulAssignment", "src": "4403:23:1", "value": {"arguments": [{"name": "size", "nodeType": "YulIdentifier", "src": "4415:4:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4421:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4411:3:1"}, "nodeType": "YulFunctionCall", "src": "4411:15:1"}, "variableNames": [{"name": "size", "nodeType": "YulIdentifier", "src": "4403:4:1"}]}]}, "name": "array_allocation_size_t_string_memory_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "length", "nodeType": "YulTypedName", "src": "4176:6:1", "type": ""}], "returnVariables": [{"name": "size", "nodeType": "YulTypedName", "src": "4187:4:1", "type": ""}], "src": "4125:308:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4503:82:1", "statements": [{"expression": {"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "4526:3:1"}, {"name": "src", "nodeType": "YulIdentifier", "src": "4531:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "4536:6:1"}], "functionName": {"name": "calldatacopy", "nodeType": "YulIdentifier", "src": "4513:12:1"}, "nodeType": "YulFunctionCall", "src": "4513:30:1"}, "nodeType": "YulExpressionStatement", "src": "4513:30:1"}, {"expression": {"arguments": [{"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "4563:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "4568:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4559:3:1"}, "nodeType": "YulFunctionCall", "src": "4559:16:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4577:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4552:6:1"}, "nodeType": "YulFunctionCall", "src": "4552:27:1"}, "nodeType": "YulExpressionStatement", "src": "4552:27:1"}]}, "name": "copy_calldata_to_memory_with_cleanup", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nodeType": "YulTypedName", "src": "4485:3:1", "type": ""}, {"name": "dst", "nodeType": "YulTypedName", "src": "4490:3:1", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "4495:6:1", "type": ""}], "src": "4439:146:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4675:341:1", "statements": [{"nodeType": "YulAssignment", "src": "4685:75:1", "value": {"arguments": [{"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "4752:6:1"}], "functionName": {"name": "array_allocation_size_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "4710:41:1"}, "nodeType": "YulFunctionCall", "src": "4710:49:1"}], "functionName": {"name": "allocate_memory", "nodeType": "YulIdentifier", "src": "4694:15:1"}, "nodeType": "YulFunctionCall", "src": "4694:66:1"}, "variableNames": [{"name": "array", "nodeType": "YulIdentifier", "src": "4685:5:1"}]}, {"expression": {"arguments": [{"name": "array", "nodeType": "YulIdentifier", "src": "4776:5:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "4783:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4769:6:1"}, "nodeType": "YulFunctionCall", "src": "4769:21:1"}, "nodeType": "YulExpressionStatement", "src": "4769:21:1"}, {"nodeType": "YulVariableDeclaration", "src": "4799:27:1", "value": {"arguments": [{"name": "array", "nodeType": "YulIdentifier", "src": "4814:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4821:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4810:3:1"}, "nodeType": "YulFunctionCall", "src": "4810:16:1"}, "variables": [{"name": "dst", "nodeType": "YulTypedName", "src": "4803:3:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4864:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_987264b3b1d58a9c7f8255e93e81c77d86d6299019c33110a076957a3e06e2ae", "nodeType": "YulIdentifier", "src": "4866:77:1"}, "nodeType": "YulFunctionCall", "src": "4866:79:1"}, "nodeType": "YulExpressionStatement", "src": "4866:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "4845:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "4850:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4841:3:1"}, "nodeType": "YulFunctionCall", "src": "4841:16:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "4859:3:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4838:2:1"}, "nodeType": "YulFunctionCall", "src": "4838:25:1"}, "nodeType": "YulIf", "src": "4835:112:1"}, {"expression": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "4993:3:1"}, {"name": "dst", "nodeType": "YulIdentifier", "src": "4998:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "5003:6:1"}], "functionName": {"name": "copy_calldata_to_memory_with_cleanup", "nodeType": "YulIdentifier", "src": "4956:36:1"}, "nodeType": "YulFunctionCall", "src": "4956:54:1"}, "nodeType": "YulExpressionStatement", "src": "4956:54:1"}]}, "name": "abi_decode_available_length_t_string_memory_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nodeType": "YulTypedName", "src": "4648:3:1", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "4653:6:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "4661:3:1", "type": ""}], "returnVariables": [{"name": "array", "nodeType": "YulTypedName", "src": "4669:5:1", "type": ""}], "src": "4591:425:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5098:278:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5147:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_1b9f4a0a5773e33b91aa01db23bf8c55fce1411167c872835e7fa00a4f17d46d", "nodeType": "YulIdentifier", "src": "5149:77:1"}, "nodeType": "YulFunctionCall", "src": "5149:79:1"}, "nodeType": "YulExpressionStatement", "src": "5149:79:1"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "5126:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5134:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5122:3:1"}, "nodeType": "YulFunctionCall", "src": "5122:17:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "5141:3:1"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "5118:3:1"}, "nodeType": "YulFunctionCall", "src": "5118:27:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "5111:6:1"}, "nodeType": "YulFunctionCall", "src": "5111:35:1"}, "nodeType": "YulIf", "src": "5108:122:1"}, {"nodeType": "YulVariableDeclaration", "src": "5239:34:1", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "5266:6:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "5253:12:1"}, "nodeType": "YulFunctionCall", "src": "5253:20:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "5243:6:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "5282:88:1", "value": {"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "5343:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5351:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5339:3:1"}, "nodeType": "YulFunctionCall", "src": "5339:17:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "5358:6:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "5366:3:1"}], "functionName": {"name": "abi_decode_available_length_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "5291:47:1"}, "nodeType": "YulFunctionCall", "src": "5291:79:1"}, "variableNames": [{"name": "array", "nodeType": "YulIdentifier", "src": "5282:5:1"}]}]}, "name": "abi_decode_t_string_memory_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "5076:6:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "5084:3:1", "type": ""}], "returnVariables": [{"name": "array", "nodeType": "YulTypedName", "src": "5092:5:1", "type": ""}], "src": "5036:340:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5502:859:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5548:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nodeType": "YulIdentifier", "src": "5550:77:1"}, "nodeType": "YulFunctionCall", "src": "5550:79:1"}, "nodeType": "YulExpressionStatement", "src": "5550:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "5523:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "5532:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "5519:3:1"}, "nodeType": "YulFunctionCall", "src": "5519:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5544:2:1", "type": "", "value": "96"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "5515:3:1"}, "nodeType": "YulFunctionCall", "src": "5515:32:1"}, "nodeType": "YulIf", "src": "5512:119:1"}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5641:287:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "5656:45:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5687:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5698:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5683:3:1"}, "nodeType": "YulFunctionCall", "src": "5683:17:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "5670:12:1"}, "nodeType": "YulFunctionCall", "src": "5670:31:1"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "5660:6:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5748:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nodeType": "YulIdentifier", "src": "5750:77:1"}, "nodeType": "YulFunctionCall", "src": "5750:79:1"}, "nodeType": "YulExpressionStatement", "src": "5750:79:1"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "5720:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5728:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "5717:2:1"}, "nodeType": "YulFunctionCall", "src": "5717:30:1"}, "nodeType": "YulIf", "src": "5714:117:1"}, {"nodeType": "YulAssignment", "src": "5845:73:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5890:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "5901:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5886:3:1"}, "nodeType": "YulFunctionCall", "src": "5886:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "5910:7:1"}], "functionName": {"name": "abi_decode_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "5855:30:1"}, "nodeType": "YulFunctionCall", "src": "5855:63:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "5845:6:1"}]}]}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5938:288:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "5953:46:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5984:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5995:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5980:3:1"}, "nodeType": "YulFunctionCall", "src": "5980:18:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "5967:12:1"}, "nodeType": "YulFunctionCall", "src": "5967:32:1"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "5957:6:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6046:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nodeType": "YulIdentifier", "src": "6048:77:1"}, "nodeType": "YulFunctionCall", "src": "6048:79:1"}, "nodeType": "YulExpressionStatement", "src": "6048:79:1"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "6018:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6026:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "6015:2:1"}, "nodeType": "YulFunctionCall", "src": "6015:30:1"}, "nodeType": "YulIf", "src": "6012:117:1"}, {"nodeType": "YulAssignment", "src": "6143:73:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6188:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "6199:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6184:3:1"}, "nodeType": "YulFunctionCall", "src": "6184:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "6208:7:1"}], "functionName": {"name": "abi_decode_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "6153:30:1"}, "nodeType": "YulFunctionCall", "src": "6153:63:1"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "6143:6:1"}]}]}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6236:118:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "6251:16:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6265:2:1", "type": "", "value": "64"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "6255:6:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "6281:63:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6316:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "6327:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6312:3:1"}, "nodeType": "YulFunctionCall", "src": "6312:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "6336:7:1"}], "functionName": {"name": "abi_decode_t_uint256", "nodeType": "YulIdentifier", "src": "6291:20:1"}, "nodeType": "YulFunctionCall", "src": "6291:53:1"}, "variableNames": [{"name": "value2", "nodeType": "YulIdentifier", "src": "6281:6:1"}]}]}]}, "name": "abi_decode_tuple_t_string_memory_ptrt_string_memory_ptrt_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "5456:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "5467:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "5479:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "5487:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "5495:6:1", "type": ""}], "src": "5382:979:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6465:124:1", "statements": [{"nodeType": "YulAssignment", "src": "6475:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6487:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6498:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6483:3:1"}, "nodeType": "YulFunctionCall", "src": "6483:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6475:4:1"}]}, {"expression": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "6555:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6568:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6579:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6564:3:1"}, "nodeType": "YulFunctionCall", "src": "6564:17:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "6511:43:1"}, "nodeType": "YulFunctionCall", "src": "6511:71:1"}, "nodeType": "YulExpressionStatement", "src": "6511:71:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "6437:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "6449:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "6460:4:1", "type": ""}], "src": "6367:222:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6623:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6640:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6643:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6633:6:1"}, "nodeType": "YulFunctionCall", "src": "6633:88:1"}, "nodeType": "YulExpressionStatement", "src": "6633:88:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6737:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6740:4:1", "type": "", "value": "0x22"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6730:6:1"}, "nodeType": "YulFunctionCall", "src": "6730:15:1"}, "nodeType": "YulExpressionStatement", "src": "6730:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6761:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6764:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "6754:6:1"}, "nodeType": "YulFunctionCall", "src": "6754:15:1"}, "nodeType": "YulExpressionStatement", "src": "6754:15:1"}]}, "name": "panic_error_0x22", "nodeType": "YulFunctionDefinition", "src": "6595:180:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6832:269:1", "statements": [{"nodeType": "YulAssignment", "src": "6842:22:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "6856:4:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6862:1:1", "type": "", "value": "2"}], "functionName": {"name": "div", "nodeType": "YulIdentifier", "src": "6852:3:1"}, "nodeType": "YulFunctionCall", "src": "6852:12:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "6842:6:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "6873:38:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "6903:4:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6909:1:1", "type": "", "value": "1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "6899:3:1"}, "nodeType": "YulFunctionCall", "src": "6899:12:1"}, "variables": [{"name": "outOfPlaceEncoding", "nodeType": "YulTypedName", "src": "6877:18:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6950:51:1", "statements": [{"nodeType": "YulAssignment", "src": "6964:27:1", "value": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "6978:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6986:4:1", "type": "", "value": "0x7f"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "6974:3:1"}, "nodeType": "YulFunctionCall", "src": "6974:17:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "6964:6:1"}]}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "6930:18:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "6923:6:1"}, "nodeType": "YulFunctionCall", "src": "6923:26:1"}, "nodeType": "YulIf", "src": "6920:81:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7053:42:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x22", "nodeType": "YulIdentifier", "src": "7067:16:1"}, "nodeType": "YulFunctionCall", "src": "7067:18:1"}, "nodeType": "YulExpressionStatement", "src": "7067:18:1"}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "7017:18:1"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "7040:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7048:2:1", "type": "", "value": "32"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "7037:2:1"}, "nodeType": "YulFunctionCall", "src": "7037:14:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "7014:2:1"}, "nodeType": "YulFunctionCall", "src": "7014:38:1"}, "nodeType": "YulIf", "src": "7011:84:1"}]}, "name": "extract_byte_array_length", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "6816:4:1", "type": ""}], "returnVariables": [{"name": "length", "nodeType": "YulTypedName", "src": "6825:6:1", "type": ""}], "src": "6781:320:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7161:87:1", "statements": [{"nodeType": "YulAssignment", "src": "7171:11:1", "value": {"name": "ptr", "nodeType": "YulIdentifier", "src": "7179:3:1"}, "variableNames": [{"name": "data", "nodeType": "YulIdentifier", "src": "7171:4:1"}]}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7199:1:1", "type": "", "value": "0"}, {"name": "ptr", "nodeType": "YulIdentifier", "src": "7202:3:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7192:6:1"}, "nodeType": "YulFunctionCall", "src": "7192:14:1"}, "nodeType": "YulExpressionStatement", "src": "7192:14:1"}, {"nodeType": "YulAssignment", "src": "7215:26:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7233:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7236:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "keccak256", "nodeType": "YulIdentifier", "src": "7223:9:1"}, "nodeType": "YulFunctionCall", "src": "7223:18:1"}, "variableNames": [{"name": "data", "nodeType": "YulIdentifier", "src": "7215:4:1"}]}]}, "name": "array_dataslot_t_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nodeType": "YulTypedName", "src": "7148:3:1", "type": ""}], "returnVariables": [{"name": "data", "nodeType": "YulTypedName", "src": "7156:4:1", "type": ""}], "src": "7107:141:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7298:49:1", "statements": [{"nodeType": "YulAssignment", "src": "7308:33:1", "value": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "7326:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7333:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7322:3:1"}, "nodeType": "YulFunctionCall", "src": "7322:14:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7338:2:1", "type": "", "value": "32"}], "functionName": {"name": "div", "nodeType": "YulIdentifier", "src": "7318:3:1"}, "nodeType": "YulFunctionCall", "src": "7318:23:1"}, "variableNames": [{"name": "result", "nodeType": "YulIdentifier", "src": "7308:6:1"}]}]}, "name": "divide_by_32_ceil", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "7281:5:1", "type": ""}], "returnVariables": [{"name": "result", "nodeType": "YulTypedName", "src": "7291:6:1", "type": ""}], "src": "7254:93:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7406:54:1", "statements": [{"nodeType": "YulAssignment", "src": "7416:37:1", "value": {"arguments": [{"name": "bits", "nodeType": "YulIdentifier", "src": "7441:4:1"}, {"name": "value", "nodeType": "YulIdentifier", "src": "7447:5:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "7437:3:1"}, "nodeType": "YulFunctionCall", "src": "7437:16:1"}, "variableNames": [{"name": "newValue", "nodeType": "YulIdentifier", "src": "7416:8:1"}]}]}, "name": "shift_left_dynamic", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "bits", "nodeType": "YulTypedName", "src": "7381:4:1", "type": ""}, {"name": "value", "nodeType": "YulTypedName", "src": "7387:5:1", "type": ""}], "returnVariables": [{"name": "newValue", "nodeType": "YulTypedName", "src": "7397:8:1", "type": ""}], "src": "7353:107:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7542:317:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "7552:35:1", "value": {"arguments": [{"name": "shiftBytes", "nodeType": "YulIdentifier", "src": "7573:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7585:1:1", "type": "", "value": "8"}], "functionName": {"name": "mul", "nodeType": "YulIdentifier", "src": "7569:3:1"}, "nodeType": "YulFunctionCall", "src": "7569:18:1"}, "variables": [{"name": "shiftBits", "nodeType": "YulTypedName", "src": "7556:9:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "7596:109:1", "value": {"arguments": [{"name": "shiftBits", "nodeType": "YulIdentifier", "src": "7627:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7638:66:1", "type": "", "value": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"}], "functionName": {"name": "shift_left_dynamic", "nodeType": "YulIdentifier", "src": "7608:18:1"}, "nodeType": "YulFunctionCall", "src": "7608:97:1"}, "variables": [{"name": "mask", "nodeType": "YulTypedName", "src": "7600:4:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "7714:51:1", "value": {"arguments": [{"name": "shiftBits", "nodeType": "YulIdentifier", "src": "7745:9:1"}, {"name": "toInsert", "nodeType": "YulIdentifier", "src": "7756:8:1"}], "functionName": {"name": "shift_left_dynamic", "nodeType": "YulIdentifier", "src": "7726:18:1"}, "nodeType": "YulFunctionCall", "src": "7726:39:1"}, "variableNames": [{"name": "toInsert", "nodeType": "YulIdentifier", "src": "7714:8:1"}]}, {"nodeType": "YulAssignment", "src": "7774:30:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "7787:5:1"}, {"arguments": [{"name": "mask", "nodeType": "YulIdentifier", "src": "7798:4:1"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "7794:3:1"}, "nodeType": "YulFunctionCall", "src": "7794:9:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "7783:3:1"}, "nodeType": "YulFunctionCall", "src": "7783:21:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "7774:5:1"}]}, {"nodeType": "YulAssignment", "src": "7813:40:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "7826:5:1"}, {"arguments": [{"name": "toInsert", "nodeType": "YulIdentifier", "src": "7837:8:1"}, {"name": "mask", "nodeType": "YulIdentifier", "src": "7847:4:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "7833:3:1"}, "nodeType": "YulFunctionCall", "src": "7833:19:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "7823:2:1"}, "nodeType": "YulFunctionCall", "src": "7823:30:1"}, "variableNames": [{"name": "result", "nodeType": "YulIdentifier", "src": "7813:6:1"}]}]}, "name": "update_byte_slice_dynamic32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "7503:5:1", "type": ""}, {"name": "shiftBytes", "nodeType": "YulTypedName", "src": "7510:10:1", "type": ""}, {"name": "toInsert", "nodeType": "YulTypedName", "src": "7522:8:1", "type": ""}], "returnVariables": [{"name": "result", "nodeType": "YulTypedName", "src": "7535:6:1", "type": ""}], "src": "7466:393:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7897:28:1", "statements": [{"nodeType": "YulAssignment", "src": "7907:12:1", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "7914:5:1"}, "variableNames": [{"name": "ret", "nodeType": "YulIdentifier", "src": "7907:3:1"}]}]}, "name": "identity", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "7883:5:1", "type": ""}], "returnVariables": [{"name": "ret", "nodeType": "YulTypedName", "src": "7893:3:1", "type": ""}], "src": "7865:60:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7991:82:1", "statements": [{"nodeType": "YulAssignment", "src": "8001:66:1", "value": {"arguments": [{"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "8059:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "8041:17:1"}, "nodeType": "YulFunctionCall", "src": "8041:24:1"}], "functionName": {"name": "identity", "nodeType": "YulIdentifier", "src": "8032:8:1"}, "nodeType": "YulFunctionCall", "src": "8032:34:1"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "8014:17:1"}, "nodeType": "YulFunctionCall", "src": "8014:53:1"}, "variableNames": [{"name": "converted", "nodeType": "YulIdentifier", "src": "8001:9:1"}]}]}, "name": "convert_t_uint256_to_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "7971:5:1", "type": ""}], "returnVariables": [{"name": "converted", "nodeType": "YulTypedName", "src": "7981:9:1", "type": ""}], "src": "7931:142:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8126:28:1", "statements": [{"nodeType": "YulAssignment", "src": "8136:12:1", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "8143:5:1"}, "variableNames": [{"name": "ret", "nodeType": "YulIdentifier", "src": "8136:3:1"}]}]}, "name": "prepare_store_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "8112:5:1", "type": ""}], "returnVariables": [{"name": "ret", "nodeType": "YulTypedName", "src": "8122:3:1", "type": ""}], "src": "8079:75:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8236:193:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "8246:63:1", "value": {"arguments": [{"name": "value_0", "nodeType": "YulIdentifier", "src": "8301:7:1"}], "functionName": {"name": "convert_t_uint256_to_t_uint256", "nodeType": "YulIdentifier", "src": "8270:30:1"}, "nodeType": "YulFunctionCall", "src": "8270:39:1"}, "variables": [{"name": "convertedValue_0", "nodeType": "YulTypedName", "src": "8250:16:1", "type": ""}]}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "8325:4:1"}, {"arguments": [{"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "8365:4:1"}], "functionName": {"name": "sload", "nodeType": "YulIdentifier", "src": "8359:5:1"}, "nodeType": "YulFunctionCall", "src": "8359:11:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "8372:6:1"}, {"arguments": [{"name": "convertedValue_0", "nodeType": "YulIdentifier", "src": "8404:16:1"}], "functionName": {"name": "prepare_store_t_uint256", "nodeType": "YulIdentifier", "src": "8380:23:1"}, "nodeType": "YulFunctionCall", "src": "8380:41:1"}], "functionName": {"name": "update_byte_slice_dynamic32", "nodeType": "YulIdentifier", "src": "8331:27:1"}, "nodeType": "YulFunctionCall", "src": "8331:91:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "8318:6:1"}, "nodeType": "YulFunctionCall", "src": "8318:105:1"}, "nodeType": "YulExpressionStatement", "src": "8318:105:1"}]}, "name": "update_storage_value_t_uint256_to_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "slot", "nodeType": "YulTypedName", "src": "8213:4:1", "type": ""}, {"name": "offset", "nodeType": "YulTypedName", "src": "8219:6:1", "type": ""}, {"name": "value_0", "nodeType": "YulTypedName", "src": "8227:7:1", "type": ""}], "src": "8160:269:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8484:24:1", "statements": [{"nodeType": "YulAssignment", "src": "8494:8:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8501:1:1", "type": "", "value": "0"}, "variableNames": [{"name": "ret", "nodeType": "YulIdentifier", "src": "8494:3:1"}]}]}, "name": "zero_value_for_split_t_uint256", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "ret", "nodeType": "YulTypedName", "src": "8480:3:1", "type": ""}], "src": "8435:73:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8567:136:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "8577:46:1", "value": {"arguments": [], "functionName": {"name": "zero_value_for_split_t_uint256", "nodeType": "YulIdentifier", "src": "8591:30:1"}, "nodeType": "YulFunctionCall", "src": "8591:32:1"}, "variables": [{"name": "zero_0", "nodeType": "YulTypedName", "src": "8581:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "8676:4:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "8682:6:1"}, {"name": "zero_0", "nodeType": "YulIdentifier", "src": "8690:6:1"}], "functionName": {"name": "update_storage_value_t_uint256_to_t_uint256", "nodeType": "YulIdentifier", "src": "8632:43:1"}, "nodeType": "YulFunctionCall", "src": "8632:65:1"}, "nodeType": "YulExpressionStatement", "src": "8632:65:1"}]}, "name": "storage_set_to_zero_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "slot", "nodeType": "YulTypedName", "src": "8553:4:1", "type": ""}, {"name": "offset", "nodeType": "YulTypedName", "src": "8559:6:1", "type": ""}], "src": "8514:189:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8759:136:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8826:63:1", "statements": [{"expression": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "8870:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8877:1:1", "type": "", "value": "0"}], "functionName": {"name": "storage_set_to_zero_t_uint256", "nodeType": "YulIdentifier", "src": "8840:29:1"}, "nodeType": "YulFunctionCall", "src": "8840:39:1"}, "nodeType": "YulExpressionStatement", "src": "8840:39:1"}]}, "condition": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "8779:5:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "8786:3:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "8776:2:1"}, "nodeType": "YulFunctionCall", "src": "8776:14:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8791:26:1", "statements": [{"nodeType": "YulAssignment", "src": "8793:22:1", "value": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "8806:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8813:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8802:3:1"}, "nodeType": "YulFunctionCall", "src": "8802:13:1"}, "variableNames": [{"name": "start", "nodeType": "YulIdentifier", "src": "8793:5:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8773:2:1", "statements": []}, "src": "8769:120:1"}]}, "name": "clear_storage_range_t_bytes1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "start", "nodeType": "YulTypedName", "src": "8747:5:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "8754:3:1", "type": ""}], "src": "8709:186:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8980:464:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9006:431:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "9020:54:1", "value": {"arguments": [{"name": "array", "nodeType": "YulIdentifier", "src": "9068:5:1"}], "functionName": {"name": "array_dataslot_t_string_storage", "nodeType": "YulIdentifier", "src": "9036:31:1"}, "nodeType": "YulFunctionCall", "src": "9036:38:1"}, "variables": [{"name": "dataArea", "nodeType": "YulTypedName", "src": "9024:8:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "9087:63:1", "value": {"arguments": [{"name": "dataArea", "nodeType": "YulIdentifier", "src": "9110:8:1"}, {"arguments": [{"name": "startIndex", "nodeType": "YulIdentifier", "src": "9138:10:1"}], "functionName": {"name": "divide_by_32_ceil", "nodeType": "YulIdentifier", "src": "9120:17:1"}, "nodeType": "YulFunctionCall", "src": "9120:29:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9106:3:1"}, "nodeType": "YulFunctionCall", "src": "9106:44:1"}, "variables": [{"name": "deleteStart", "nodeType": "YulTypedName", "src": "9091:11:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9307:27:1", "statements": [{"nodeType": "YulAssignment", "src": "9309:23:1", "value": {"name": "dataArea", "nodeType": "YulIdentifier", "src": "9324:8:1"}, "variableNames": [{"name": "deleteStart", "nodeType": "YulIdentifier", "src": "9309:11:1"}]}]}, "condition": {"arguments": [{"name": "startIndex", "nodeType": "YulIdentifier", "src": "9291:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9303:2:1", "type": "", "value": "32"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "9288:2:1"}, "nodeType": "YulFunctionCall", "src": "9288:18:1"}, "nodeType": "YulIf", "src": "9285:49:1"}, {"expression": {"arguments": [{"name": "deleteStart", "nodeType": "YulIdentifier", "src": "9376:11:1"}, {"arguments": [{"name": "dataArea", "nodeType": "YulIdentifier", "src": "9393:8:1"}, {"arguments": [{"name": "len", "nodeType": "YulIdentifier", "src": "9421:3:1"}], "functionName": {"name": "divide_by_32_ceil", "nodeType": "YulIdentifier", "src": "9403:17:1"}, "nodeType": "YulFunctionCall", "src": "9403:22:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9389:3:1"}, "nodeType": "YulFunctionCall", "src": "9389:37:1"}], "functionName": {"name": "clear_storage_range_t_bytes1", "nodeType": "YulIdentifier", "src": "9347:28:1"}, "nodeType": "YulFunctionCall", "src": "9347:80:1"}, "nodeType": "YulExpressionStatement", "src": "9347:80:1"}]}, "condition": {"arguments": [{"name": "len", "nodeType": "YulIdentifier", "src": "8997:3:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9002:2:1", "type": "", "value": "31"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "8994:2:1"}, "nodeType": "YulFunctionCall", "src": "8994:11:1"}, "nodeType": "YulIf", "src": "8991:446:1"}]}, "name": "clean_up_bytearray_end_slots_t_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "array", "nodeType": "YulTypedName", "src": "8956:5:1", "type": ""}, {"name": "len", "nodeType": "YulTypedName", "src": "8963:3:1", "type": ""}, {"name": "startIndex", "nodeType": "YulTypedName", "src": "8968:10:1", "type": ""}], "src": "8901:543:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9513:54:1", "statements": [{"nodeType": "YulAssignment", "src": "9523:37:1", "value": {"arguments": [{"name": "bits", "nodeType": "YulIdentifier", "src": "9548:4:1"}, {"name": "value", "nodeType": "YulIdentifier", "src": "9554:5:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "9544:3:1"}, "nodeType": "YulFunctionCall", "src": "9544:16:1"}, "variableNames": [{"name": "newValue", "nodeType": "YulIdentifier", "src": "9523:8:1"}]}]}, "name": "shift_right_unsigned_dynamic", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "bits", "nodeType": "YulTypedName", "src": "9488:4:1", "type": ""}, {"name": "value", "nodeType": "YulTypedName", "src": "9494:5:1", "type": ""}], "returnVariables": [{"name": "newValue", "nodeType": "YulTypedName", "src": "9504:8:1", "type": ""}], "src": "9450:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9624:118:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "9634:68:1", "value": {"arguments": [{"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9683:1:1", "type": "", "value": "8"}, {"name": "bytes", "nodeType": "YulIdentifier", "src": "9686:5:1"}], "functionName": {"name": "mul", "nodeType": "YulIdentifier", "src": "9679:3:1"}, "nodeType": "YulFunctionCall", "src": "9679:13:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9698:1:1", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "9694:3:1"}, "nodeType": "YulFunctionCall", "src": "9694:6:1"}], "functionName": {"name": "shift_right_unsigned_dynamic", "nodeType": "YulIdentifier", "src": "9650:28:1"}, "nodeType": "YulFunctionCall", "src": "9650:51:1"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "9646:3:1"}, "nodeType": "YulFunctionCall", "src": "9646:56:1"}, "variables": [{"name": "mask", "nodeType": "YulTypedName", "src": "9638:4:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "9711:25:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "9725:4:1"}, {"name": "mask", "nodeType": "YulIdentifier", "src": "9731:4:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "9721:3:1"}, "nodeType": "YulFunctionCall", "src": "9721:15:1"}, "variableNames": [{"name": "result", "nodeType": "YulIdentifier", "src": "9711:6:1"}]}]}, "name": "mask_bytes_dynamic", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "9601:4:1", "type": ""}, {"name": "bytes", "nodeType": "YulTypedName", "src": "9607:5:1", "type": ""}], "returnVariables": [{"name": "result", "nodeType": "YulTypedName", "src": "9617:6:1", "type": ""}], "src": "9573:169:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9828:214:1", "statements": [{"nodeType": "YulAssignment", "src": "9961:37:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "9988:4:1"}, {"name": "len", "nodeType": "YulIdentifier", "src": "9994:3:1"}], "functionName": {"name": "mask_bytes_dynamic", "nodeType": "YulIdentifier", "src": "9969:18:1"}, "nodeType": "YulFunctionCall", "src": "9969:29:1"}, "variableNames": [{"name": "data", "nodeType": "YulIdentifier", "src": "9961:4:1"}]}, {"nodeType": "YulAssignment", "src": "10007:29:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "10018:4:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10028:1:1", "type": "", "value": "2"}, {"name": "len", "nodeType": "YulIdentifier", "src": "10031:3:1"}], "functionName": {"name": "mul", "nodeType": "YulIdentifier", "src": "10024:3:1"}, "nodeType": "YulFunctionCall", "src": "10024:11:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "10015:2:1"}, "nodeType": "YulFunctionCall", "src": "10015:21:1"}, "variableNames": [{"name": "used", "nodeType": "YulIdentifier", "src": "10007:4:1"}]}]}, "name": "extract_used_part_and_set_length_of_short_byte_array", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "9809:4:1", "type": ""}, {"name": "len", "nodeType": "YulTypedName", "src": "9815:3:1", "type": ""}], "returnVariables": [{"name": "used", "nodeType": "YulTypedName", "src": "9823:4:1", "type": ""}], "src": "9747:295:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10139:1303:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "10150:51:1", "value": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "10197:3:1"}], "functionName": {"name": "array_length_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "10164:32:1"}, "nodeType": "YulFunctionCall", "src": "10164:37:1"}, "variables": [{"name": "newLen", "nodeType": "YulTypedName", "src": "10154:6:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10286:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "10288:16:1"}, "nodeType": "YulFunctionCall", "src": "10288:18:1"}, "nodeType": "YulExpressionStatement", "src": "10288:18:1"}]}, "condition": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "10258:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10266:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "10255:2:1"}, "nodeType": "YulFunctionCall", "src": "10255:30:1"}, "nodeType": "YulIf", "src": "10252:56:1"}, {"nodeType": "YulVariableDeclaration", "src": "10318:52:1", "value": {"arguments": [{"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "10364:4:1"}], "functionName": {"name": "sload", "nodeType": "YulIdentifier", "src": "10358:5:1"}, "nodeType": "YulFunctionCall", "src": "10358:11:1"}], "functionName": {"name": "extract_byte_array_length", "nodeType": "YulIdentifier", "src": "10332:25:1"}, "nodeType": "YulFunctionCall", "src": "10332:38:1"}, "variables": [{"name": "old<PERSON>en", "nodeType": "YulTypedName", "src": "10322:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "10463:4:1"}, {"name": "old<PERSON>en", "nodeType": "YulIdentifier", "src": "10469:6:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "10477:6:1"}], "functionName": {"name": "clean_up_bytearray_end_slots_t_string_storage", "nodeType": "YulIdentifier", "src": "10417:45:1"}, "nodeType": "YulFunctionCall", "src": "10417:67:1"}, "nodeType": "YulExpressionStatement", "src": "10417:67:1"}, {"nodeType": "YulVariableDeclaration", "src": "10494:18:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10511:1:1", "type": "", "value": "0"}, "variables": [{"name": "srcOffset", "nodeType": "YulTypedName", "src": "10498:9:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "10522:17:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10535:4:1", "type": "", "value": "0x20"}, "variableNames": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "10522:9:1"}]}, {"cases": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10586:611:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "10600:37:1", "value": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "10619:6:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10631:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "10627:3:1"}, "nodeType": "YulFunctionCall", "src": "10627:9:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "10615:3:1"}, "nodeType": "YulFunctionCall", "src": "10615:22:1"}, "variables": [{"name": "loopEnd", "nodeType": "YulTypedName", "src": "10604:7:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "10651:51:1", "value": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "10697:4:1"}], "functionName": {"name": "array_dataslot_t_string_storage", "nodeType": "YulIdentifier", "src": "10665:31:1"}, "nodeType": "YulFunctionCall", "src": "10665:37:1"}, "variables": [{"name": "dstPtr", "nodeType": "YulTypedName", "src": "10655:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "10715:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10724:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "10719:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10783:163:1", "statements": [{"expression": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "10808:6:1"}, {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "10826:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "10831:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10822:3:1"}, "nodeType": "YulFunctionCall", "src": "10822:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "10816:5:1"}, "nodeType": "YulFunctionCall", "src": "10816:26:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "10801:6:1"}, "nodeType": "YulFunctionCall", "src": "10801:42:1"}, "nodeType": "YulExpressionStatement", "src": "10801:42:1"}, {"nodeType": "YulAssignment", "src": "10860:24:1", "value": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "10874:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10882:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10870:3:1"}, "nodeType": "YulFunctionCall", "src": "10870:14:1"}, "variableNames": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "10860:6:1"}]}, {"nodeType": "YulAssignment", "src": "10901:31:1", "value": {"arguments": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "10918:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10929:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10914:3:1"}, "nodeType": "YulFunctionCall", "src": "10914:18:1"}, "variableNames": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "10901:9:1"}]}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "10749:1:1"}, {"name": "loopEnd", "nodeType": "YulIdentifier", "src": "10752:7:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "10746:2:1"}, "nodeType": "YulFunctionCall", "src": "10746:14:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10761:21:1", "statements": [{"nodeType": "YulAssignment", "src": "10763:17:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "10772:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10775:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10768:3:1"}, "nodeType": "YulFunctionCall", "src": "10768:12:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "10763:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10742:3:1", "statements": []}, "src": "10738:208:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10982:156:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "11000:43:1", "value": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "11027:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "11032:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11023:3:1"}, "nodeType": "YulFunctionCall", "src": "11023:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "11017:5:1"}, "nodeType": "YulFunctionCall", "src": "11017:26:1"}, "variables": [{"name": "lastValue", "nodeType": "YulTypedName", "src": "11004:9:1", "type": ""}]}, {"expression": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "11067:6:1"}, {"arguments": [{"name": "lastValue", "nodeType": "YulIdentifier", "src": "11094:9:1"}, {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "11109:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11117:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "11105:3:1"}, "nodeType": "YulFunctionCall", "src": "11105:17:1"}], "functionName": {"name": "mask_bytes_dynamic", "nodeType": "YulIdentifier", "src": "11075:18:1"}, "nodeType": "YulFunctionCall", "src": "11075:48:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "11060:6:1"}, "nodeType": "YulFunctionCall", "src": "11060:64:1"}, "nodeType": "YulExpressionStatement", "src": "11060:64:1"}]}, "condition": {"arguments": [{"name": "loopEnd", "nodeType": "YulIdentifier", "src": "10965:7:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "10974:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "10962:2:1"}, "nodeType": "YulFunctionCall", "src": "10962:19:1"}, "nodeType": "YulIf", "src": "10959:179:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "11158:4:1"}, {"arguments": [{"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "11172:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11180:1:1", "type": "", "value": "2"}], "functionName": {"name": "mul", "nodeType": "YulIdentifier", "src": "11168:3:1"}, "nodeType": "YulFunctionCall", "src": "11168:14:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11184:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11164:3:1"}, "nodeType": "YulFunctionCall", "src": "11164:22:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "11151:6:1"}, "nodeType": "YulFunctionCall", "src": "11151:36:1"}, "nodeType": "YulExpressionStatement", "src": "11151:36:1"}]}, "nodeType": "YulCase", "src": "10579:618:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10584:1:1", "type": "", "value": "1"}}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11214:222:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "11228:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "11241:1:1", "type": "", "value": "0"}, "variables": [{"name": "value", "nodeType": "YulTypedName", "src": "11232:5:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "11265:67:1", "statements": [{"nodeType": "YulAssignment", "src": "11283:35:1", "value": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "11302:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "11307:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "11298:3:1"}, "nodeType": "YulFunctionCall", "src": "11298:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "11292:5:1"}, "nodeType": "YulFunctionCall", "src": "11292:26:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "11283:5:1"}]}]}, "condition": {"name": "newLen", "nodeType": "YulIdentifier", "src": "11258:6:1"}, "nodeType": "YulIf", "src": "11255:77:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "11352:4:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "11411:5:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "11418:6:1"}], "functionName": {"name": "extract_used_part_and_set_length_of_short_byte_array", "nodeType": "YulIdentifier", "src": "11358:52:1"}, "nodeType": "YulFunctionCall", "src": "11358:67:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "11345:6:1"}, "nodeType": "YulFunctionCall", "src": "11345:81:1"}, "nodeType": "YulExpressionStatement", "src": "11345:81:1"}]}, "nodeType": "YulCase", "src": "11206:230:1", "value": "default"}], "expression": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "10559:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10567:2:1", "type": "", "value": "31"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "10556:2:1"}, "nodeType": "YulFunctionCall", "src": "10556:14:1"}, "nodeType": "YulSwitch", "src": "10549:887:1"}]}, "name": "copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "slot", "nodeType": "YulTypedName", "src": "10128:4:1", "type": ""}, {"name": "src", "nodeType": "YulTypedName", "src": "10134:3:1", "type": ""}], "src": "10047:1395:1"}]}, "contents": "{\n\n    function allocate_unbounded() -> memPtr {\n        memPtr := mload(64)\n    }\n\n    function revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() {\n        revert(0, 0)\n    }\n\n    function revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() {\n        revert(0, 0)\n    }\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function validator_revert_t_uint256(value) {\n        if iszero(eq(value, cleanup_t_uint256(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint256(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint256(value)\n    }\n\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function array_length_t_string_memory_ptr(value) -> length {\n\n        length := mload(value)\n\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function copy_memory_to_memory_with_cleanup(src, dst, length) {\n        let i := 0\n        for { } lt(i, length) { i := add(i, 32) }\n        {\n            mstore(add(dst, i), mload(add(src, i)))\n        }\n        mstore(add(dst, length), 0)\n    }\n\n    function round_up_to_mul_of_32(value) -> result {\n        result := and(add(value, 31), not(31))\n    }\n\n    function abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value, pos) -> end {\n        let length := array_length_t_string_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length)\n        copy_memory_to_memory_with_cleanup(add(value, 0x20), pos, length)\n        end := add(pos, round_up_to_mul_of_32(length))\n    }\n\n    function cleanup_t_uint160(value) -> cleaned {\n        cleaned := and(value, 0xffffffffffffffffffffffffffffffffffffffff)\n    }\n\n    function cleanup_t_address(value) -> cleaned {\n        cleaned := cleanup_t_uint160(value)\n    }\n\n    function abi_encode_t_address_to_t_address_fromStack(value, pos) {\n        mstore(pos, cleanup_t_address(value))\n    }\n\n    function abi_encode_t_uint256_to_t_uint256_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encode_tuple_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__fromStack_reversed(headStart , value3, value2, value1, value0) -> tail {\n        tail := add(headStart, 128)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value0,  tail)\n\n        abi_encode_t_address_to_t_address_fromStack(value1,  add(headStart, 32))\n\n        mstore(add(headStart, 64), sub(tail, headStart))\n        tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value2,  tail)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value3,  add(headStart, 96))\n\n    }\n\n    function revert_error_1b9f4a0a5773e33b91aa01db23bf8c55fce1411167c872835e7fa00a4f17d46d() {\n        revert(0, 0)\n    }\n\n    function revert_error_987264b3b1d58a9c7f8255e93e81c77d86d6299019c33110a076957a3e06e2ae() {\n        revert(0, 0)\n    }\n\n    function panic_error_0x41() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x41)\n        revert(0, 0x24)\n    }\n\n    function finalize_allocation(memPtr, size) {\n        let newFreePtr := add(memPtr, round_up_to_mul_of_32(size))\n        // protect against overflow\n        if or(gt(newFreePtr, 0xffffffffffffffff), lt(newFreePtr, memPtr)) { panic_error_0x41() }\n        mstore(64, newFreePtr)\n    }\n\n    function allocate_memory(size) -> memPtr {\n        memPtr := allocate_unbounded()\n        finalize_allocation(memPtr, size)\n    }\n\n    function array_allocation_size_t_string_memory_ptr(length) -> size {\n        // Make sure we can allocate memory without overflow\n        if gt(length, 0xffffffffffffffff) { panic_error_0x41() }\n\n        size := round_up_to_mul_of_32(length)\n\n        // add length slot\n        size := add(size, 0x20)\n\n    }\n\n    function copy_calldata_to_memory_with_cleanup(src, dst, length) {\n        calldatacopy(dst, src, length)\n        mstore(add(dst, length), 0)\n    }\n\n    function abi_decode_available_length_t_string_memory_ptr(src, length, end) -> array {\n        array := allocate_memory(array_allocation_size_t_string_memory_ptr(length))\n        mstore(array, length)\n        let dst := add(array, 0x20)\n        if gt(add(src, length), end) { revert_error_987264b3b1d58a9c7f8255e93e81c77d86d6299019c33110a076957a3e06e2ae() }\n        copy_calldata_to_memory_with_cleanup(src, dst, length)\n    }\n\n    // string\n    function abi_decode_t_string_memory_ptr(offset, end) -> array {\n        if iszero(slt(add(offset, 0x1f), end)) { revert_error_1b9f4a0a5773e33b91aa01db23bf8c55fce1411167c872835e7fa00a4f17d46d() }\n        let length := calldataload(offset)\n        array := abi_decode_available_length_t_string_memory_ptr(add(offset, 0x20), length, end)\n    }\n\n    function abi_decode_tuple_t_string_memory_ptrt_string_memory_ptrt_uint256(headStart, dataEnd) -> value0, value1, value2 {\n        if slt(sub(dataEnd, headStart), 96) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := calldataload(add(headStart, 0))\n            if gt(offset, 0xffffffffffffffff) { revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() }\n\n            value0 := abi_decode_t_string_memory_ptr(add(headStart, offset), dataEnd)\n        }\n\n        {\n\n            let offset := calldataload(add(headStart, 32))\n            if gt(offset, 0xffffffffffffffff) { revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() }\n\n            value1 := abi_decode_t_string_memory_ptr(add(headStart, offset), dataEnd)\n        }\n\n        {\n\n            let offset := 64\n\n            value2 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function panic_error_0x22() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x22)\n        revert(0, 0x24)\n    }\n\n    function extract_byte_array_length(data) -> length {\n        length := div(data, 2)\n        let outOfPlaceEncoding := and(data, 1)\n        if iszero(outOfPlaceEncoding) {\n            length := and(length, 0x7f)\n        }\n\n        if eq(outOfPlaceEncoding, lt(length, 32)) {\n            panic_error_0x22()\n        }\n    }\n\n    function array_dataslot_t_string_storage(ptr) -> data {\n        data := ptr\n\n        mstore(0, ptr)\n        data := keccak256(0, 0x20)\n\n    }\n\n    function divide_by_32_ceil(value) -> result {\n        result := div(add(value, 31), 32)\n    }\n\n    function shift_left_dynamic(bits, value) -> newValue {\n        newValue :=\n\n        shl(bits, value)\n\n    }\n\n    function update_byte_slice_dynamic32(value, shiftBytes, toInsert) -> result {\n        let shiftBits := mul(shiftBytes, 8)\n        let mask := shift_left_dynamic(shiftBits, 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff)\n        toInsert := shift_left_dynamic(shiftBits, toInsert)\n        value := and(value, not(mask))\n        result := or(value, and(toInsert, mask))\n    }\n\n    function identity(value) -> ret {\n        ret := value\n    }\n\n    function convert_t_uint256_to_t_uint256(value) -> converted {\n        converted := cleanup_t_uint256(identity(cleanup_t_uint256(value)))\n    }\n\n    function prepare_store_t_uint256(value) -> ret {\n        ret := value\n    }\n\n    function update_storage_value_t_uint256_to_t_uint256(slot, offset, value_0) {\n        let convertedValue_0 := convert_t_uint256_to_t_uint256(value_0)\n        sstore(slot, update_byte_slice_dynamic32(sload(slot), offset, prepare_store_t_uint256(convertedValue_0)))\n    }\n\n    function zero_value_for_split_t_uint256() -> ret {\n        ret := 0\n    }\n\n    function storage_set_to_zero_t_uint256(slot, offset) {\n        let zero_0 := zero_value_for_split_t_uint256()\n        update_storage_value_t_uint256_to_t_uint256(slot, offset, zero_0)\n    }\n\n    function clear_storage_range_t_bytes1(start, end) {\n        for {} lt(start, end) { start := add(start, 1) }\n        {\n            storage_set_to_zero_t_uint256(start, 0)\n        }\n    }\n\n    function clean_up_bytearray_end_slots_t_string_storage(array, len, startIndex) {\n\n        if gt(len, 31) {\n            let dataArea := array_dataslot_t_string_storage(array)\n            let deleteStart := add(dataArea, divide_by_32_ceil(startIndex))\n            // If we are clearing array to be short byte array, we want to clear only data starting from array data area.\n            if lt(startIndex, 32) { deleteStart := dataArea }\n            clear_storage_range_t_bytes1(deleteStart, add(dataArea, divide_by_32_ceil(len)))\n        }\n\n    }\n\n    function shift_right_unsigned_dynamic(bits, value) -> newValue {\n        newValue :=\n\n        shr(bits, value)\n\n    }\n\n    function mask_bytes_dynamic(data, bytes) -> result {\n        let mask := not(shift_right_unsigned_dynamic(mul(8, bytes), not(0)))\n        result := and(data, mask)\n    }\n    function extract_used_part_and_set_length_of_short_byte_array(data, len) -> used {\n        // we want to save only elements that are part of the array after resizing\n        // others should be set to zero\n        data := mask_bytes_dynamic(data, len)\n        used := or(data, mul(2, len))\n    }\n    function copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage(slot, src) {\n\n        let newLen := array_length_t_string_memory_ptr(src)\n        // Make sure array length is sane\n        if gt(newLen, 0xffffffffffffffff) { panic_error_0x41() }\n\n        let oldLen := extract_byte_array_length(sload(slot))\n\n        // potentially truncate data\n        clean_up_bytearray_end_slots_t_string_storage(slot, oldLen, newLen)\n\n        let srcOffset := 0\n\n        srcOffset := 0x20\n\n        switch gt(newLen, 31)\n        case 1 {\n            let loopEnd := and(newLen, not(0x1f))\n\n            let dstPtr := array_dataslot_t_string_storage(slot)\n            let i := 0\n            for { } lt(i, loopEnd) { i := add(i, 0x20) } {\n                sstore(dstPtr, mload(add(src, srcOffset)))\n                dstPtr := add(dstPtr, 1)\n                srcOffset := add(srcOffset, 32)\n            }\n            if lt(loopEnd, newLen) {\n                let lastValue := mload(add(src, srcOffset))\n                sstore(dstPtr, mask_bytes_dynamic(lastValue, and(newLen, 0x1f)))\n            }\n            sstore(slot, add(mul(newLen, 2), 1))\n        }\n        default {\n            let value := 0\n            if newLen {\n                value := mload(add(src, srcOffset))\n            }\n            sstore(slot, extract_used_part_and_set_length_of_short_byte_array(value, newLen))\n        }\n    }\n\n}\n", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "608060405234801561001057600080fd5b50600436106100415760003560e01c8063304cff301461004657806330753c7a14610079578063a8ec42d814610095575b600080fd5b610060600480360381019061005b919061035f565b6100b3565b604051610070949392919061046c565b60405180910390f35b610093600480360381019061008e91906105f4565b610223565b005b61009d610309565b6040516100aa919061067f565b60405180910390f35b600081815481106100c357600080fd5b90600052602060002090600402016000915090508060000180546100e6906106c9565b80601f0160208091040260200160405190810160405280929190818152602001828054610112906106c9565b801561015f5780601f106101345761010080835404028352916020019161015f565b820191906000526020600020905b81548152906001019060200180831161014257829003601f168201915b5050505050908060010160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff169080600201805461019a906106c9565b80601f01602080910402602001604051908101604052809291908181526020018280546101c6906106c9565b80156102135780601f106101e857610100808354040283529160200191610213565b820191906000526020600020905b8154815290600101906020018083116101f657829003601f168201915b5050505050908060030154905084565b600060405180608001604052808581526020013373ffffffffffffffffffffffffffffffffffffffff168152602001848152602001838152509080600181540180825580915050600190039060005260206000209060040201600090919091909150600082015181600001908161029a91906108a6565b5060208201518160010160006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555060408201518160020190816102f791906108a6565b50606082015181600301555050505050565b60008080549050905090565b6000604051905090565b600080fd5b600080fd5b6000819050919050565b61033c81610329565b811461034757600080fd5b50565b60008135905061035981610333565b92915050565b6000602082840312156103755761037461031f565b5b60006103838482850161034a565b91505092915050565b600081519050919050565b600082825260208201905092915050565b60005b838110156103c65780820151818401526020810190506103ab565b60008484015250505050565b6000601f19601f8301169050919050565b60006103ee8261038c565b6103f88185610397565b93506104088185602086016103a8565b610411816103d2565b840191505092915050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b60006104478261041c565b9050919050565b6104578161043c565b82525050565b61046681610329565b82525050565b6000608082019050818103600083015261048681876103e3565b9050610495602083018661044e565b81810360408301526104a781856103e3565b90506104b6606083018461045d565b95945050505050565b600080fd5b600080fd5b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b610501826103d2565b810181811067ffffffffffffffff821117156105205761051f6104c9565b5b80604052505050565b6000610533610315565b905061053f82826104f8565b919050565b600067ffffffffffffffff82111561055f5761055e6104c9565b5b610568826103d2565b9050602081019050919050565b82818337600083830152505050565b600061059761059284610544565b610529565b9050828152602081018484840111156105b3576105b26104c4565b5b6105be848285610575565b509392505050565b600082601f8301126105db576105da6104bf565b5b81356105eb848260208601610584565b91505092915050565b60008060006060848603121561060d5761060c61031f565b5b600084013567ffffffffffffffff81111561062b5761062a610324565b5b610637868287016105c6565b935050602084013567ffffffffffffffff81111561065857610657610324565b5b610664868287016105c6565b92505060406106758682870161034a565b9150509250925092565b6000602082019050610694600083018461045d565b92915050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b600060028204905060018216806106e157607f821691505b6020821081036106f4576106f361069a565b5b50919050565b60008190508160005260206000209050919050565b60006020601f8301049050919050565b600082821b905092915050565b60006008830261075c7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8261071f565b610766868361071f565b95508019841693508086168417925050509392505050565b6000819050919050565b60006107a361079e61079984610329565b61077e565b610329565b9050919050565b6000819050919050565b6107bd83610788565b6107d16107c9826107aa565b84845461072c565b825550505050565b600090565b6107e66107d9565b6107f18184846107b4565b505050565b5b818110156108155761080a6000826107de565b6001810190506107f7565b5050565b601f82111561085a5761082b816106fa565b6108348461070f565b81016020851015610843578190505b61085761084f8561070f565b8301826107f6565b50505b505050565b600082821c905092915050565b600061087d6000198460080261085f565b1980831691505092915050565b6000610896838361086c565b9150826002028217905092915050565b6108af8261038c565b67ffffffffffffffff8111156108c8576108c76104c9565b5b6108d282546106c9565b6108dd828285610819565b600060209050601f83116001811461091057600084156108fe578287015190505b610908858261088a565b865550610970565b601f19841661091e866106fa565b60005b8281101561094657848901518255600182019150602085019450602081019050610921565b86831015610963578489015161095f601f89168261086c565b8355505b6001600288020188555050505b50505050505056fea2646970667358221220a1fdc587185816be20e4917b9ff72f3273abbb234e278db13f9123bd4c5f7bd664736f6c63430008140033", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x41 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x304CFF30 EQ PUSH2 0x46 JUMPI DUP1 PUSH4 0x30753C7A EQ PUSH2 0x79 JUMPI DUP1 PUSH4 0xA8EC42D8 EQ PUSH2 0x95 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x60 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x5B SWAP2 SWAP1 PUSH2 0x35F JUMP JUMPDEST PUSH2 0xB3 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x70 SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x46C JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x93 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x8E SWAP2 SWAP1 PUSH2 0x5F4 JUMP JUMPDEST PUSH2 0x223 JUMP JUMPDEST STOP JUMPDEST PUSH2 0x9D PUSH2 0x309 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xAA SWAP2 SWAP1 PUSH2 0x67F JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 DUP2 DUP2 SLOAD DUP2 LT PUSH2 0xC3 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x4 MUL ADD PUSH1 0x0 SWAP2 POP SWAP1 POP DUP1 PUSH1 0x0 ADD DUP1 SLOAD PUSH2 0xE6 SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x112 SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x15F JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x134 JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x15F JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x142 JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP SWAP1 DUP1 PUSH1 0x1 ADD PUSH1 0x0 SWAP1 SLOAD SWAP1 PUSH2 0x100 EXP SWAP1 DIV PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND SWAP1 DUP1 PUSH1 0x2 ADD DUP1 SLOAD PUSH2 0x19A SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 PUSH1 0x1F ADD PUSH1 0x20 DUP1 SWAP2 DIV MUL PUSH1 0x20 ADD PUSH1 0x40 MLOAD SWAP1 DUP2 ADD PUSH1 0x40 MSTORE DUP1 SWAP3 SWAP2 SWAP1 DUP2 DUP2 MSTORE PUSH1 0x20 ADD DUP3 DUP1 SLOAD PUSH2 0x1C6 SWAP1 PUSH2 0x6C9 JUMP JUMPDEST DUP1 ISZERO PUSH2 0x213 JUMPI DUP1 PUSH1 0x1F LT PUSH2 0x1E8 JUMPI PUSH2 0x100 DUP1 DUP4 SLOAD DIV MUL DUP4 MSTORE SWAP2 PUSH1 0x20 ADD SWAP2 PUSH2 0x213 JUMP JUMPDEST DUP3 ADD SWAP2 SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 JUMPDEST DUP2 SLOAD DUP2 MSTORE SWAP1 PUSH1 0x1 ADD SWAP1 PUSH1 0x20 ADD DUP1 DUP4 GT PUSH2 0x1F6 JUMPI DUP3 SWAP1 SUB PUSH1 0x1F AND DUP3 ADD SWAP2 JUMPDEST POP POP POP POP POP SWAP1 DUP1 PUSH1 0x3 ADD SLOAD SWAP1 POP DUP5 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 PUSH1 0x80 ADD PUSH1 0x40 MSTORE DUP1 DUP6 DUP2 MSTORE PUSH1 0x20 ADD CALLER PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD DUP5 DUP2 MSTORE PUSH1 0x20 ADD DUP4 DUP2 MSTORE POP SWAP1 DUP1 PUSH1 0x1 DUP2 SLOAD ADD DUP1 DUP3 SSTORE DUP1 SWAP2 POP POP PUSH1 0x1 SWAP1 SUB SWAP1 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x4 MUL ADD PUSH1 0x0 SWAP1 SWAP2 SWAP1 SWAP2 SWAP1 SWAP2 POP PUSH1 0x0 DUP3 ADD MLOAD DUP2 PUSH1 0x0 ADD SWAP1 DUP2 PUSH2 0x29A SWAP2 SWAP1 PUSH2 0x8A6 JUMP JUMPDEST POP PUSH1 0x20 DUP3 ADD MLOAD DUP2 PUSH1 0x1 ADD PUSH1 0x0 PUSH2 0x100 EXP DUP2 SLOAD DUP2 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF MUL NOT AND SWAP1 DUP4 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND MUL OR SWAP1 SSTORE POP PUSH1 0x40 DUP3 ADD MLOAD DUP2 PUSH1 0x2 ADD SWAP1 DUP2 PUSH2 0x2F7 SWAP2 SWAP1 PUSH2 0x8A6 JUMP JUMPDEST POP PUSH1 0x60 DUP3 ADD MLOAD DUP2 PUSH1 0x3 ADD SSTORE POP POP POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 DUP1 SLOAD SWAP1 POP SWAP1 POP SWAP1 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD SWAP1 POP SWAP1 JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x33C DUP2 PUSH2 0x329 JUMP JUMPDEST DUP2 EQ PUSH2 0x347 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x359 DUP2 PUSH2 0x333 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x375 JUMPI PUSH2 0x374 PUSH2 0x31F JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x383 DUP5 DUP3 DUP6 ADD PUSH2 0x34A JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x3C6 JUMPI DUP1 DUP3 ADD MLOAD DUP2 DUP5 ADD MSTORE PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x3AB JUMP JUMPDEST PUSH1 0x0 DUP5 DUP5 ADD MSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x3EE DUP3 PUSH2 0x38C JUMP JUMPDEST PUSH2 0x3F8 DUP2 DUP6 PUSH2 0x397 JUMP JUMPDEST SWAP4 POP PUSH2 0x408 DUP2 DUP6 PUSH1 0x20 DUP7 ADD PUSH2 0x3A8 JUMP JUMPDEST PUSH2 0x411 DUP2 PUSH2 0x3D2 JUMP JUMPDEST DUP5 ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x447 DUP3 PUSH2 0x41C JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x457 DUP2 PUSH2 0x43C JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH2 0x466 DUP2 PUSH2 0x329 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x80 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x486 DUP2 DUP8 PUSH2 0x3E3 JUMP JUMPDEST SWAP1 POP PUSH2 0x495 PUSH1 0x20 DUP4 ADD DUP7 PUSH2 0x44E JUMP JUMPDEST DUP2 DUP2 SUB PUSH1 0x40 DUP4 ADD MSTORE PUSH2 0x4A7 DUP2 DUP6 PUSH2 0x3E3 JUMP JUMPDEST SWAP1 POP PUSH2 0x4B6 PUSH1 0x60 DUP4 ADD DUP5 PUSH2 0x45D JUMP JUMPDEST SWAP6 SWAP5 POP POP POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH2 0x501 DUP3 PUSH2 0x3D2 JUMP JUMPDEST DUP2 ADD DUP2 DUP2 LT PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT OR ISZERO PUSH2 0x520 JUMPI PUSH2 0x51F PUSH2 0x4C9 JUMP JUMPDEST JUMPDEST DUP1 PUSH1 0x40 MSTORE POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x533 PUSH2 0x315 JUMP JUMPDEST SWAP1 POP PUSH2 0x53F DUP3 DUP3 PUSH2 0x4F8 JUMP JUMPDEST SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT ISZERO PUSH2 0x55F JUMPI PUSH2 0x55E PUSH2 0x4C9 JUMP JUMPDEST JUMPDEST PUSH2 0x568 DUP3 PUSH2 0x3D2 JUMP JUMPDEST SWAP1 POP PUSH1 0x20 DUP2 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST DUP3 DUP2 DUP4 CALLDATACOPY PUSH1 0x0 DUP4 DUP4 ADD MSTORE POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x597 PUSH2 0x592 DUP5 PUSH2 0x544 JUMP JUMPDEST PUSH2 0x529 JUMP JUMPDEST SWAP1 POP DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP5 DUP5 DUP5 ADD GT ISZERO PUSH2 0x5B3 JUMPI PUSH2 0x5B2 PUSH2 0x4C4 JUMP JUMPDEST JUMPDEST PUSH2 0x5BE DUP5 DUP3 DUP6 PUSH2 0x575 JUMP JUMPDEST POP SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP3 PUSH1 0x1F DUP4 ADD SLT PUSH2 0x5DB JUMPI PUSH2 0x5DA PUSH2 0x4BF JUMP JUMPDEST JUMPDEST DUP2 CALLDATALOAD PUSH2 0x5EB DUP5 DUP3 PUSH1 0x20 DUP7 ADD PUSH2 0x584 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x0 PUSH1 0x60 DUP5 DUP7 SUB SLT ISZERO PUSH2 0x60D JUMPI PUSH2 0x60C PUSH2 0x31F JUMP JUMPDEST JUMPDEST PUSH1 0x0 DUP5 ADD CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x62B JUMPI PUSH2 0x62A PUSH2 0x324 JUMP JUMPDEST JUMPDEST PUSH2 0x637 DUP7 DUP3 DUP8 ADD PUSH2 0x5C6 JUMP JUMPDEST SWAP4 POP POP PUSH1 0x20 DUP5 ADD CALLDATALOAD PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x658 JUMPI PUSH2 0x657 PUSH2 0x324 JUMP JUMPDEST JUMPDEST PUSH2 0x664 DUP7 DUP3 DUP8 ADD PUSH2 0x5C6 JUMP JUMPDEST SWAP3 POP POP PUSH1 0x40 PUSH2 0x675 DUP7 DUP3 DUP8 ADD PUSH2 0x34A JUMP JUMPDEST SWAP2 POP POP SWAP3 POP SWAP3 POP SWAP3 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x694 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x45D JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH1 0x2 DUP3 DIV SWAP1 POP PUSH1 0x1 DUP3 AND DUP1 PUSH2 0x6E1 JUMPI PUSH1 0x7F DUP3 AND SWAP2 POP JUMPDEST PUSH1 0x20 DUP3 LT DUP2 SUB PUSH2 0x6F4 JUMPI PUSH2 0x6F3 PUSH2 0x69A JUMP JUMPDEST JUMPDEST POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP DUP2 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 PUSH1 0x1F DUP4 ADD DIV SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 SHL SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x8 DUP4 MUL PUSH2 0x75C PUSH32 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF DUP3 PUSH2 0x71F JUMP JUMPDEST PUSH2 0x766 DUP7 DUP4 PUSH2 0x71F JUMP JUMPDEST SWAP6 POP DUP1 NOT DUP5 AND SWAP4 POP DUP1 DUP7 AND DUP5 OR SWAP3 POP POP POP SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7A3 PUSH2 0x79E PUSH2 0x799 DUP5 PUSH2 0x329 JUMP JUMPDEST PUSH2 0x77E JUMP JUMPDEST PUSH2 0x329 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x7BD DUP4 PUSH2 0x788 JUMP JUMPDEST PUSH2 0x7D1 PUSH2 0x7C9 DUP3 PUSH2 0x7AA JUMP JUMPDEST DUP5 DUP5 SLOAD PUSH2 0x72C JUMP JUMPDEST DUP3 SSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 SWAP1 JUMP JUMPDEST PUSH2 0x7E6 PUSH2 0x7D9 JUMP JUMPDEST PUSH2 0x7F1 DUP2 DUP5 DUP5 PUSH2 0x7B4 JUMP JUMPDEST POP POP POP JUMP JUMPDEST JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x815 JUMPI PUSH2 0x80A PUSH1 0x0 DUP3 PUSH2 0x7DE JUMP JUMPDEST PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x7F7 JUMP JUMPDEST POP POP JUMP JUMPDEST PUSH1 0x1F DUP3 GT ISZERO PUSH2 0x85A JUMPI PUSH2 0x82B DUP2 PUSH2 0x6FA JUMP JUMPDEST PUSH2 0x834 DUP5 PUSH2 0x70F JUMP JUMPDEST DUP2 ADD PUSH1 0x20 DUP6 LT ISZERO PUSH2 0x843 JUMPI DUP2 SWAP1 POP JUMPDEST PUSH2 0x857 PUSH2 0x84F DUP6 PUSH2 0x70F JUMP JUMPDEST DUP4 ADD DUP3 PUSH2 0x7F6 JUMP JUMPDEST POP POP JUMPDEST POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 SHR SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x87D PUSH1 0x0 NOT DUP5 PUSH1 0x8 MUL PUSH2 0x85F JUMP JUMPDEST NOT DUP1 DUP4 AND SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x896 DUP4 DUP4 PUSH2 0x86C JUMP JUMPDEST SWAP2 POP DUP3 PUSH1 0x2 MUL DUP3 OR SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH2 0x8AF DUP3 PUSH2 0x38C JUMP JUMPDEST PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x8C8 JUMPI PUSH2 0x8C7 PUSH2 0x4C9 JUMP JUMPDEST JUMPDEST PUSH2 0x8D2 DUP3 SLOAD PUSH2 0x6C9 JUMP JUMPDEST PUSH2 0x8DD DUP3 DUP3 DUP6 PUSH2 0x819 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 SWAP1 POP PUSH1 0x1F DUP4 GT PUSH1 0x1 DUP2 EQ PUSH2 0x910 JUMPI PUSH1 0x0 DUP5 ISZERO PUSH2 0x8FE JUMPI DUP3 DUP8 ADD MLOAD SWAP1 POP JUMPDEST PUSH2 0x908 DUP6 DUP3 PUSH2 0x88A JUMP JUMPDEST DUP7 SSTORE POP PUSH2 0x970 JUMP JUMPDEST PUSH1 0x1F NOT DUP5 AND PUSH2 0x91E DUP7 PUSH2 0x6FA JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP3 DUP2 LT ISZERO PUSH2 0x946 JUMPI DUP5 DUP10 ADD MLOAD DUP3 SSTORE PUSH1 0x1 DUP3 ADD SWAP2 POP PUSH1 0x20 DUP6 ADD SWAP5 POP PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x921 JUMP JUMPDEST DUP7 DUP4 LT ISZERO PUSH2 0x963 JUMPI DUP5 DUP10 ADD MLOAD PUSH2 0x95F PUSH1 0x1F DUP10 AND DUP3 PUSH2 0x86C JUMP JUMPDEST DUP4 SSTORE POP JUMPDEST PUSH1 0x1 PUSH1 0x2 DUP9 MUL ADD DUP9 SSTORE POP POP POP JUMPDEST POP POP POP POP POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 LOG1 REVERT 0xC5 DUP8 XOR PC AND 0xBE KECCAK256 0xE4 SWAP2 PUSH28 0x9FF72F3273ABBB234E278DB13F9123BD4C5F7BD664736F6C63430008 EQ STOP CALLER ", "sourceMap": "61:491:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;210:19;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;289:156;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;453:96;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;210:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;289:156::-;387:5;398:38;;;;;;;;403:6;398:38;;;;411:10;398:38;;;;;;423:4;398:38;;;;429:6;398:38;;;387:50;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;289:156;;;:::o;453:96::-;502:7;529:5;:12;;;;522:19;;453:96;:::o;7:75:1:-;40:6;73:2;67:9;57:19;;7:75;:::o;88:117::-;197:1;194;187:12;211:117;320:1;317;310:12;334:77;371:7;400:5;389:16;;334:77;;;:::o;417:122::-;490:24;508:5;490:24;:::i;:::-;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:139::-;591:5;629:6;616:20;607:29;;645:33;672:5;645:33;:::i;:::-;545:139;;;;:::o;690:329::-;749:6;798:2;786:9;777:7;773:23;769:32;766:119;;;804:79;;:::i;:::-;766:119;924:1;949:53;994:7;985:6;974:9;970:22;949:53;:::i;:::-;939:63;;895:117;690:329;;;;:::o;1025:99::-;1077:6;1111:5;1105:12;1095:22;;1025:99;;;:::o;1130:169::-;1214:11;1248:6;1243:3;1236:19;1288:4;1283:3;1279:14;1264:29;;1130:169;;;;:::o;1305:246::-;1386:1;1396:113;1410:6;1407:1;1404:13;1396:113;;;1495:1;1490:3;1486:11;1480:18;1476:1;1471:3;1467:11;1460:39;1432:2;1429:1;1425:10;1420:15;;1396:113;;;1543:1;1534:6;1529:3;1525:16;1518:27;1367:184;1305:246;;;:::o;1557:102::-;1598:6;1649:2;1645:7;1640:2;1633:5;1629:14;1625:28;1615:38;;1557:102;;;:::o;1665:377::-;1753:3;1781:39;1814:5;1781:39;:::i;:::-;1836:71;1900:6;1895:3;1836:71;:::i;:::-;1829:78;;1916:65;1974:6;1969:3;1962:4;1955:5;1951:16;1916:65;:::i;:::-;2006:29;2028:6;2006:29;:::i;:::-;2001:3;1997:39;1990:46;;1757:285;1665:377;;;;:::o;2048:126::-;2085:7;2125:42;2118:5;2114:54;2103:65;;2048:126;;;:::o;2180:96::-;2217:7;2246:24;2264:5;2246:24;:::i;:::-;2235:35;;2180:96;;;:::o;2282:118::-;2369:24;2387:5;2369:24;:::i;:::-;2364:3;2357:37;2282:118;;:::o;2406:::-;2493:24;2511:5;2493:24;:::i;:::-;2488:3;2481:37;2406:118;;:::o;2530:735::-;2747:4;2785:3;2774:9;2770:19;2762:27;;2835:9;2829:4;2825:20;2821:1;2810:9;2806:17;2799:47;2863:78;2936:4;2927:6;2863:78;:::i;:::-;2855:86;;2951:72;3019:2;3008:9;3004:18;2995:6;2951:72;:::i;:::-;3070:9;3064:4;3060:20;3055:2;3044:9;3040:18;3033:48;3098:78;3171:4;3162:6;3098:78;:::i;:::-;3090:86;;3186:72;3254:2;3243:9;3239:18;3230:6;3186:72;:::i;:::-;2530:735;;;;;;;:::o;3271:117::-;3380:1;3377;3370:12;3394:117;3503:1;3500;3493:12;3517:180;3565:77;3562:1;3555:88;3662:4;3659:1;3652:15;3686:4;3683:1;3676:15;3703:281;3786:27;3808:4;3786:27;:::i;:::-;3778:6;3774:40;3916:6;3904:10;3901:22;3880:18;3868:10;3865:34;3862:62;3859:88;;;3927:18;;:::i;:::-;3859:88;3967:10;3963:2;3956:22;3746:238;3703:281;;:::o;3990:129::-;4024:6;4051:20;;:::i;:::-;4041:30;;4080:33;4108:4;4100:6;4080:33;:::i;:::-;3990:129;;;:::o;4125:308::-;4187:4;4277:18;4269:6;4266:30;4263:56;;;4299:18;;:::i;:::-;4263:56;4337:29;4359:6;4337:29;:::i;:::-;4329:37;;4421:4;4415;4411:15;4403:23;;4125:308;;;:::o;4439:146::-;4536:6;4531:3;4526;4513:30;4577:1;4568:6;4563:3;4559:16;4552:27;4439:146;;;:::o;4591:425::-;4669:5;4694:66;4710:49;4752:6;4710:49;:::i;:::-;4694:66;:::i;:::-;4685:75;;4783:6;4776:5;4769:21;4821:4;4814:5;4810:16;4859:3;4850:6;4845:3;4841:16;4838:25;4835:112;;;4866:79;;:::i;:::-;4835:112;4956:54;5003:6;4998:3;4993;4956:54;:::i;:::-;4675:341;4591:425;;;;;:::o;5036:340::-;5092:5;5141:3;5134:4;5126:6;5122:17;5118:27;5108:122;;5149:79;;:::i;:::-;5108:122;5266:6;5253:20;5291:79;5366:3;5358:6;5351:4;5343:6;5339:17;5291:79;:::i;:::-;5282:88;;5098:278;5036:340;;;;:::o;5382:979::-;5479:6;5487;5495;5544:2;5532:9;5523:7;5519:23;5515:32;5512:119;;;5550:79;;:::i;:::-;5512:119;5698:1;5687:9;5683:17;5670:31;5728:18;5720:6;5717:30;5714:117;;;5750:79;;:::i;:::-;5714:117;5855:63;5910:7;5901:6;5890:9;5886:22;5855:63;:::i;:::-;5845:73;;5641:287;5995:2;5984:9;5980:18;5967:32;6026:18;6018:6;6015:30;6012:117;;;6048:79;;:::i;:::-;6012:117;6153:63;6208:7;6199:6;6188:9;6184:22;6153:63;:::i;:::-;6143:73;;5938:288;6265:2;6291:53;6336:7;6327:6;6316:9;6312:22;6291:53;:::i;:::-;6281:63;;6236:118;5382:979;;;;;:::o;6367:222::-;6460:4;6498:2;6487:9;6483:18;6475:26;;6511:71;6579:1;6568:9;6564:17;6555:6;6511:71;:::i;:::-;6367:222;;;;:::o;6595:180::-;6643:77;6640:1;6633:88;6740:4;6737:1;6730:15;6764:4;6761:1;6754:15;6781:320;6825:6;6862:1;6856:4;6852:12;6842:22;;6909:1;6903:4;6899:12;6930:18;6920:81;;6986:4;6978:6;6974:17;6964:27;;6920:81;7048:2;7040:6;7037:14;7017:18;7014:38;7011:84;;7067:18;;:::i;:::-;7011:84;6832:269;6781:320;;;:::o;7107:141::-;7156:4;7179:3;7171:11;;7202:3;7199:1;7192:14;7236:4;7233:1;7223:18;7215:26;;7107:141;;;:::o;7254:93::-;7291:6;7338:2;7333;7326:5;7322:14;7318:23;7308:33;;7254:93;;;:::o;7353:107::-;7397:8;7447:5;7441:4;7437:16;7416:37;;7353:107;;;;:::o;7466:393::-;7535:6;7585:1;7573:10;7569:18;7608:97;7638:66;7627:9;7608:97;:::i;:::-;7726:39;7756:8;7745:9;7726:39;:::i;:::-;7714:51;;7798:4;7794:9;7787:5;7783:21;7774:30;;7847:4;7837:8;7833:19;7826:5;7823:30;7813:40;;7542:317;;7466:393;;;;;:::o;7865:60::-;7893:3;7914:5;7907:12;;7865:60;;;:::o;7931:142::-;7981:9;8014:53;8032:34;8041:24;8059:5;8041:24;:::i;:::-;8032:34;:::i;:::-;8014:53;:::i;:::-;8001:66;;7931:142;;;:::o;8079:75::-;8122:3;8143:5;8136:12;;8079:75;;;:::o;8160:269::-;8270:39;8301:7;8270:39;:::i;:::-;8331:91;8380:41;8404:16;8380:41;:::i;:::-;8372:6;8365:4;8359:11;8331:91;:::i;:::-;8325:4;8318:105;8236:193;8160:269;;;:::o;8435:73::-;8480:3;8435:73;:::o;8514:189::-;8591:32;;:::i;:::-;8632:65;8690:6;8682;8676:4;8632:65;:::i;:::-;8567:136;8514:189;;:::o;8709:186::-;8769:120;8786:3;8779:5;8776:14;8769:120;;;8840:39;8877:1;8870:5;8840:39;:::i;:::-;8813:1;8806:5;8802:13;8793:22;;8769:120;;;8709:186;;:::o;8901:543::-;9002:2;8997:3;8994:11;8991:446;;;9036:38;9068:5;9036:38;:::i;:::-;9120:29;9138:10;9120:29;:::i;:::-;9110:8;9106:44;9303:2;9291:10;9288:18;9285:49;;;9324:8;9309:23;;9285:49;9347:80;9403:22;9421:3;9403:22;:::i;:::-;9393:8;9389:37;9376:11;9347:80;:::i;:::-;9006:431;;8991:446;8901:543;;;:::o;9450:117::-;9504:8;9554:5;9548:4;9544:16;9523:37;;9450:117;;;;:::o;9573:169::-;9617:6;9650:51;9698:1;9694:6;9686:5;9683:1;9679:13;9650:51;:::i;:::-;9646:56;9731:4;9725;9721:15;9711:25;;9624:118;9573:169;;;;:::o;9747:295::-;9823:4;9969:29;9994:3;9988:4;9969:29;:::i;:::-;9961:37;;10031:3;10028:1;10024:11;10018:4;10015:21;10007:29;;9747:295;;;;:::o;10047:1395::-;10164:37;10197:3;10164:37;:::i;:::-;10266:18;10258:6;10255:30;10252:56;;;10288:18;;:::i;:::-;10252:56;10332:38;10364:4;10358:11;10332:38;:::i;:::-;10417:67;10477:6;10469;10463:4;10417:67;:::i;:::-;10511:1;10535:4;10522:17;;10567:2;10559:6;10556:14;10584:1;10579:618;;;;11241:1;11258:6;11255:77;;;11307:9;11302:3;11298:19;11292:26;11283:35;;11255:77;11358:67;11418:6;11411:5;11358:67;:::i;:::-;11352:4;11345:81;11214:222;10549:887;;10579:618;10631:4;10627:9;10619:6;10615:22;10665:37;10697:4;10665:37;:::i;:::-;10724:1;10738:208;10752:7;10749:1;10746:14;10738:208;;;10831:9;10826:3;10822:19;10816:26;10808:6;10801:42;10882:1;10874:6;10870:14;10860:24;;10929:2;10918:9;10914:18;10901:31;;10775:4;10772:1;10768:12;10763:17;;10738:208;;;10974:6;10965:7;10962:19;10959:179;;;11032:9;11027:3;11023:19;11017:26;11075:48;11117:4;11109:6;11105:17;11094:9;11075:48;:::i;:::-;11067:6;11060:64;10982:156;10959:179;11184:1;11180;11172:6;11168:14;11164:22;11158:4;11151:36;10586:611;;;10549:887;;10139:1303;;;10047:1395;;:::o"}, "methodIdentifiers": {"getNumberOfSongs()": "a8ec42d8", "registerSong(string,string,uint256)": "30753c7a", "songs(uint256)": "304cff30"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"getNumberOfSongs\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_title\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_url\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_price\",\"type\":\"uint256\"}],\"name\":\"registerSong\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"songs\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"title\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"url\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"song-registry-contracts/contracts/songRegistry.sol\":\"SongRegistry\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"song-registry-contracts/contracts/songRegistry.sol\":{\"keccak256\":\"0xb44b308d05a3bec36355485add7cd49eea6b42f321db1dc9f18125fab2d830c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9556ac018c64e0428d44c724c7f09944e027b42552cebd1f9f332e9cd4aeed6d\",\"dweb:/ipfs/QmdDXvkQ84XKu1Riu1iApTLvdqvRJXbcXvF6Mythu3F7Nj\"]}},\"version\":1}"}}}}}