{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\song-registry-smart-contract\\song-registry-contracts\\contracts\\songRegistry.sol": {"lastModificationDate": 1751964487396, "contentHash": "e8fad3bf22b531599a761bcc60e16a84", "sourceName": "song-registry-contracts/contracts/songRegistry.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.19"], "artifacts": ["SongRegistry"]}}}